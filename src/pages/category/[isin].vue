<template>
  <client-only>
    <category-form :is-edit="true" />
  </client-only>
</template>

<script setup>
import { onMounted, ref } from 'vue';
import CategoryForm from '@/components/pages/category/category-form.vue';

import { useNuxtApp } from '#app';
import { useProjectLang } from '@/composables/useProjectLang';
import { useCommonUtils } from '~/composables/useCommonUtils';

const { $keys } = useNuxtApp();

const { readyProject } = useProjectLang();
const { triggerLoading } = useCommonUtils();

onMounted(() => {
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, true);
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, false);
    }
  });
});
</script>
