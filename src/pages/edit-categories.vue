<template>
  <client-only>
    <content-wrapper class="padding-zero">
      <div class="edit-categories-main-container">
      <div class="background-image-categories" v-show="!isPageLoading">
        <img alt="background" class="background-image"
        :src="`${image}`"/>
        <div class="back-btn" v-if="route.query[QUERY_PARAM_KEY.BACK_FROM]" @click="backToCategories()">
          <img alt="back arrow" class="back-arrow-image"
          src="../assets/images/back-arrow.png"/>
          <span class="back-to-categories text-title-2">{{ $t('CATEGORY.BACK_MESSAGE') }}</span>
        </div>
        <div class="back-btn" v-if="!route.query[QUERY_PARAM_KEY.BACK_FROM]" @click="backToCategories()">
          <img alt="back arrow" class="back-arrow-image"
          src="../assets/images/back-arrow.png"/>
          <span class="back-to-categories text-title-2">{{ $t('CATEGORY_GROUP.BACK_MESSAGE') }}</span>
        </div>
        <div class="head-btn">
          <button
            type="button"
            :class="isCampaignModified && categoriesName.trim() !== '' && image && !isRecipeVariantNameEmpty && !isSlugCheckConfirm  && (uploadImagePercentage === 0 || uploadImagePercentage === 100) ? 'btn-green' : 'disabled-button btn-green'"
            @click="displayPopupAsync()"
            @keydown="preventEnterAndSpaceKeyPress($event)"
          >
            {{ isCategoriesStatus === "active" ? "Publish" : "Save" }}
          </button>
          <button type="button" @click="backToCategories()" class="btn-green-outline" @keydown="preventEnterAndSpaceKeyPress($event)">
            {{ $t('BUTTONS.CANCEL_BUTTON') }}
          </button>
        </div>
        <div Class="edit-cat-isin text-title-2 font-normal">ISIN: {{ editCategoriesISIN }}</div>
      </div>
      <div class="input-categories-section" v-show="!isPageLoading">
        <div class="input-section">
          <div class="input-sub-section">
            <div class="left-section">
              <div class="image-section" ref="categoryImage"
              >
                <div class="image-main-div" id="recipeVideo">
                  <div class="image-inner-container">
                    <div
                      class="progress-image"
                      v-show="uploadImagePercentage >= 1 && uploadImagePercentage <= 99"
                    >
                      <div class="progress-image-content">
                        <div
                          v-show="
                            uploadImagePercentage >= 1 && uploadImagePercentage <= 5
                          "
                        >
                          <img alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-0.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 6 && uploadImagePercentage <= 11
                          "
                        >
                          <img alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-6.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 12 && uploadImagePercentage <= 17
                          "
                        >
                          <img alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-12.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 18 && uploadImagePercentage <= 24
                          "
                        >
                          <img alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-18.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 25 && uploadImagePercentage <= 30
                          "
                        >
                          <img alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-25.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 31 && uploadImagePercentage <= 36
                          "
                        >
                          <img alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-31.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 37 && uploadImagePercentage <= 41
                          "
                        >
                          <img alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-37.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 42 && uploadImagePercentage <= 49
                          "
                        >
                          <img alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-42.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 50 && uploadImagePercentage <= 55
                          "
                        >
                          <img alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-50.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 56 && uploadImagePercentage <= 61
                          "
                        >
                          <img alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-56.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 62 && uploadImagePercentage <= 67
                          "
                        >
                          <img alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-62.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 68 && uploadImagePercentage <= 74
                          "
                        >
                          <img alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-68.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 75 && uploadImagePercentage <= 80
                          "
                        >
                          <img alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-75.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 81 && uploadImagePercentage <= 86
                          "
                        >
                          <img alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-81.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 87 && uploadImagePercentage <= 92
                          "
                        >
                          <img alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-87.svg?skipsvgo=true"
                          />
                        </div>
                        <div
                          v-show="
                            uploadImagePercentage >= 93 && uploadImagePercentage <= 98
                          "
                        >
                          <img alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-upload-93.svg?skipsvgo=true"
                          />
                        </div>
                        <div v-show="uploadImagePercentage == 99">
                          <img alt=""
                            class="progress-icon"
                            src="@/assets/images/icon-video-uploaded.svg?skipsvgo=true"
                          />
                        </div>
                        <div class="upload-text">
                          <div class="upload-heading text-light-h4" v-if="uploadImagePercentage >=1 && uploadImagePercentage <= 98"
                            >Upload is in progress
                          </div>
                          <div class="upload-heading text-light-h4" v-else
                            >Uploaded
                          </div>
                          <span class="upload-media text-light-h6"
                            >{{ (loadedImageSize / 1024000).toFixed(1) }} of
                            {{ (uploadImageSize / 1024000).toFixed(1) }}
                            MB</span
                          >
                        </div>
                      </div>
                    </div>
                  </div>
                  <img alt=""
                    v-if="image && (uploadImagePercentage == 0 || uploadImagePercentage == 100)"
                    class="display-image-section"
                    :src="`${image}`"
                  />
                  <div
                    class="replace-image-tag text-h3 font-normal"
                    v-if="uploadImagePercentage == 0 || uploadImagePercentage == 100"
                  >
                    <div class="hover-image">
                      <input
                        type="file"
                        class="upload-input"
                        title="Update Picture"
                        @click="uploadSameImageVideo($event)"
                        @change="checkUploadedFiles"
                        accept=".jpg,.png,.jpeg"
                        id="productVideo"
                      />
                    </div>
                  </div>
                </div>
              </div>
              <div
                class="text-section text-h4 font-light"
                :class="{
                  'simple-data-tooltip': hasRecipeTitleFocus,
                }"
                :data-tooltip-text="hasRecipeTitleFocus && categoriesName"
            >
                <img alt="compulsory"
                  v-if="!categoriesName"
                  class="compulsory-field-category"
                  src="../assets/images/asterisk.svg?skipsvgo=true"
                />
                <input
                  class="title text-title-1"
                  id="title-name"
                  @mouseover="checkEditCategorieName(categoriesName)"
                  @mouseleave="hideEditcategorynameTip()"
                  @keydown="hideEditcategorynameTip()"
                  v-if="route.query.isin"
                  v-model.trim="categoriesName"
                  autocomplete="off"
                  placeholder="Name your category"
                  @click="dropDownClose()"
                >
              </div>
              <div class="slug-category-details">
                <span class="slug-category-text text-title-3">Slug:</span>
                <input
                  autocomplete="off"
                  id="slug-categories"
                  v-model="categoriesSlug"
                  class="slug-category-input"
                  @input="slugInput(),debounceInput()"
                  @click="dropDownClose()"
                  @keypress="restrictToAlphanumeric($event)"
                >
                <div v-if="hasSlugStatus" class="slug-exist-main">
                  <p class="slug-exist text-light-h4">This slug already exists</p>
                </div>
              </div>
              <div class="image-details">
                <span class="bold text-title-2">Category Image: </span>
                <span class="normal text-title-2 font-normal">jpg,png format (recom. 1MB, max 15 MB)</span>
                <span class="compulsory-field-category-image">*</span>
              </div>
            </div>
            <div class="right-section">
                <div
                  :class="((categoriesState === 'published') || (categoriesState === 'publishing')) ? 'publish-btn' : 'publish-btn'">
                <span class="text text-title-2">
                  {{ $t('COMMON.PUBLISH')  }}
                </span>
                <label class="switch">
                  <input type="checkbox" :checked="isCategoriesStatus == 'active'"
                  @click.prevent="
                      categoriesName.length>0
                      ? publishToggleBtnAsync() : publishToggleBtnPopup()">
                  <span class="slider-round"></span>
                </label>
              </div>
              <div
                class="delete-btn"
                :class="{
                  'simple-data-tooltip simple-data-tooltip-warn': recipeDataForCategories.length || categoryPromotedRecipes.length,
                }"
              >
                <div class="simple-data-tooltip-content" v-if="recipeDataForCategories.length || categoryPromotedRecipes.length">
                  <img src="@/assets/images/info.svg?skipsvgo=true" alt="info-icon" class="tooltip-icon" />
                  <span>{{ $t('CATEGORY.CATEGORIES_DELETE_INFO') }}</span>
                </div>
                <button
                  type="button"
                  class="btn-reset"
                  @click="deleteCategoryListPopUp()"
                  :class="{
                    'simple-data-tooltip': isCategoryIncludeInHero
                  }"
                  :disabled="isCategoryIncludeInHero"
                  :data-tooltip-text="isCategoryIncludeInHero && $t('ARTICLE_IN_HERO')"
                  :id="(recipeDataForCategories.length <= 0) && (categoryPromotedRecipes.length <=0) && (recipeForCategoriesTotal === 0) && (recipeForCategoriesTotal === checktotal)    ? ' enable-button' : 'disable-button'" >
                  <img
                    alt="delete-icon"
                    class="image"
                    :class="{'disabled': isCategoryIncludeInHero}"
                    src="../assets/images/delete-icon.png"
                  />
                  <span class="text text-h3" :class="{'disabled': isCategoryIncludeInHero}">
                   {{ $t('CATEGORY.DELETE_CATEGORY') }}
                  </span>
                </button>
              </div>
            </div>
          </div>
          <div v-if="finalAvailableLangs && finalAvailableLangs.length > 1" class="category-variant-section">
            <div class="category-variants-main">
              <div class="category-variants">
              Category Variants:
                <div v-show="isCategoryAlertIcon" class="tag-variant-tooltip-section">
                  <div class="tooltip-main-container-for-tag-variant simple-data-tooltip"
                    :data-tooltip-text="langVariantTooltip"
                  >
                    <img alt="alert" class="alert-image" src="@/assets/images/red-info.svg?skipsvgo=true" >
                  </div>
                </div>
              </div>
              <div
                class="add-variant-section"
                :class="{
                  'simple-data-tooltip': recipeVariantLanguageList.length < 1,
                }"
                :data-tooltip-text="recipeVariantLanguageList.length < 1 && $t('COMMON.ADD_ONLY_ONE_VARIANT')"
              >
                <div class="add-variant-main" @click="openRecipeVariantPopUp()"
                  :class="recipeVariantLanguageList.length > 0
                  ? 'add-variant-main'
                  : 'disable-add-variant-main add-variant-main'"
                >
                  <div class="add-variant-btn">
                    <img alt="" src="@/assets/images/category-add.png">
                  </div>
                  <div class="add-variant-text text-h3">
                    Add variant
                  </div>
                </div>
              </div>
            </div>
            <div class="add-category-variant" v-if="recipeVariantList.length <= 0">
              Add category variants to support multiple languages.
            </div>
            <div class="category-variant-card-main" v-else>
              <template v-for="(categoryVariant, index) in recipeVariantList">
                <variant-card-field
                  v-if="categoryVariant?.lang !== lang"
                  v-model="categoryVariant.name"
                  :prefix-label="displayLanguageCode(categoryVariant.lang)"
                  input-placeholder="Enter name"
                  :is-delete-action-disabled="isDeleteVariantVisible(categoryVariant)"
                  delete-action-tooltip-text="Cannot delete because category variant is used by recipes or category groups."
                  @input-change="inputContentChanged()"
                  @delete-action="deleteCategoryVariant(categoryVariant, index)"
                ></variant-card-field>
              </template>
            </div>
          </div>
        </div>
      </div>
      <div :class="{'recipes-table-content': true, 'recipes-table-content-data': isPageLoading}">
        <div class="content">
          <div class="loading" v-if="isPageLoading">
            <div class="content">
              <div class="input-loading">
                <div class="loader-image"></div>
              </div>
              <div class="loader-text-container">
                <div class="loading-text">
                  <p>{{ $t('LOADER.LOADING') }}</p>
                </div>
              </div>
            </div>
          </div>
          <div class="loading" v-if="isUpdating">
            <div class="content">
              <div class="input-loading">
                <div class="loader-image"></div>
              </div>
              <div class="loader-text-container">
                <div class="loading-text">
                  <p>{{ $t('UPDATING') }}</p>
                </div>
              </div>
            </div>
          </div>
          <div class="promoted-header" v-if="!isPageLoading && !isUpdating">
            {{categoryPromotedRecipes.length}} {{ categoryPromotedRecipes.length === 1 ? $t('CATEGORY.PROMOTED_RECIPE') : $t('CATEGORY.PROMOTED_RECIPES') }}
          </div>
          <div class="promoted-subtitle" v-if="!isPageLoading && !isUpdating">
            {{ $t('CATEGORY.SHOW_CATEGORY_PROMOTED_TEXT') }}
          </div>
          <div class="add-zero-section" v-if="(!categoryPromotedRecipes || (categoryPromotedRecipes?(categoryPromotedRecipes.length?categoryPromotedRecipes.length:0):0) === 0) && !isPageLoading && !isUpdating">
            <div class="zero-promoted">
              <span class="bold text-title-2">
                0 PROMOTED RECIPES.
              </span>
              <span class="normal text-title-2 font-normal">
                Recipes will be auto-selected.
              </span>
            </div>
          </div>
          <div class="promote-table-content"
            v-if="(categoryPromotedRecipes || (categoryPromotedRecipes?(categoryPromotedRecipes.length?categoryPromotedRecipes.length:0):0) !== 0) && !isPageLoading && !isUpdating"
            :class="(!categoryPromotedRecipes || (categoryPromotedRecipes?(categoryPromotedRecipes.length?categoryPromotedRecipes.length:0):0) === 0)? 'margin-promote-table' : ''"
          >
          <div class="table-header text-h3" v-show="categoryPromotedRecipes.length>0">
                          <div class="margin-div"></div>
                          <div class="category-group-isin"><span>{{ $t('COMMON.RECIPE_ISIN') }} </span></div>
                          <div class="category-group-title"><span>{{ $t('COMMON.RECIPE_TITLE') }}</span></div>
                          <div class="category-group-total-time"><span> {{ $t('COMMON.TOTAL_TIME') }}</span></div>
                          <div class="ing-count"><span>{{ $t('COMMON.INGREDIENT_COUNT') }}</span></div>
                    </div>
          					<table class="promote-table" id="promote-table">
                      <caption></caption>
						<tbody>
              <draggable
              v-model="categoryPromotedRecipes"
              class="all-content-categories"
              :scroll-sensitivity="200"
              :force-fallback="true"
              ghost-class="hidden-list"
              @start="drag = true"
              @end="drag = false"
              handle=".draggable-icon"
              @change="handleDrag"
            >
						<tr class="body" v-for="(promote, index) in categoryPromotedRecipes" :key="index">
              <td>
                <div class="draggable-icon">
                  <div class="instruction-drag-icon ">
                    <img alt="" class="promoted-handle" src="@/assets/images/drag-vertically.svg?skipsvgo=true" />
                  </div>
                </div>
              </td>
							<td class="table-image-promote">
                <div class="image-promote" >
                  <img alt="" class="image" :src="getImageSource(promote)" loading="eager" />
                </div>
							</td>
							<td class="table-promote-code">
                <div class="promote-code">
                  {{ promote ? (promote.isin ? promote.isin : '') : '' }}
                </div>
							</td>
              <td
                class="promote-recipe-name"
                :class="{
                  'simple-data-tooltip': !hasDraggableTooltipDisplay && isCategoryListTooltipVisible
                }"
                :data-tooltip-text="!hasDraggableTooltipDisplay && isCategoryListTooltipVisible
                  ? (promote?.title?.[lang] || promote?.title)
                  : ''"
              >
								<div class="edit-category-recipe-name-tooltip">
                  <div
                    class="promote-name text-h3"
                    @mouseover="checkPromoteName(index)"
                    @mouseleave="hidePromoteNameTip(index)"
                    :id="`Promoterecipename${index}`"
                  >
                    {{ promote.title[lang] || promote.title || '' }}
                  </div>
                  <div
                    v-if="promote?.langs?.length > 1"
                    class="simple-data-tooltip"
                    :data-tooltip-text="getAvailableLanguagesTooltip(categoryPromotedRecipes)"
                  >
                    <img alt="" src="@/assets/images/language-icon.png">
                  </div>
                </div>
								<div class="promote-subtitle" v-if="promote && promote.subtitle && promote.subtitle[lang]">
									<span v-if="promote && promote.subtitle && promote.subtitle[lang] && promote.subtitle[lang].length >= 40">
										{{ promote && promote.subtitle && promote.subtitle[lang] ? promote.subtitle[lang].substring(0, 40) + "..." : " "}}
									</span>
									<span v-if="promote && promote.subtitle && promote.subtitle[lang] && promote.subtitle[lang].length < 40">
										{{ promote && promote.subtitle && promote.subtitle[lang] ? promote.subtitle[lang]: " "}}
									</span>
								</div>
                <div class="promote-subtitle" v-if="promote && promote.subtitle">
									<span v-if="promote && promote.subtitle && promote.subtitle.length >= 40">
										{{ promote && promote.subtitle ? promote.subtitle.substring(0, 40) + "..." : ""}}
									</span>
									<span v-if="promote && promote.subtitle && promote.subtitle.length < 40">
										{{ promote && promote.subtitle ? promote.subtitle: ""}}
									</span>
								</div>
							</td>
							<td class="promote-ingredient-time">
                <div class="promote-details">
									<div class="details">
                      <span v-if="promote.time && parseDurationString(promote.time.total)">{{parseDurationString(promote.time.total ? promote.time.total : '') ? parseDurationString(promote.time.total ? promote.time.total : '') : ''}}</span>
                      <span v-if="promote.totalTime && parseDurationString(promote.totalTime)">{{parseDurationString(promote.totalTime ? promote.totalTime : '') ? parseDurationString(promote.totalTime ? promote.totalTime : '') : ''}}</span>
                      <span v-if="!(promote.time && promote.time.total) && !(promote.totalTime)">none</span>
									</div>
                </div>
							</td>
              <td class="promote-ingredient-count">
                <div class="promote-details">
                  <div class="details">
                    <span
                      v-if="
                        (promote.ingredients &&
                          promote.ingredients[lang] &&
                          promote.ingredients[lang].length == 1) ||(promote.ingredients &&
                          promote.ingredients &&
                          promote.ingredients.length == 1)
                      "
                        >{{
                          (promote.ingredients && promote.ingredients[lang]
                            ? promote.ingredients[lang].length
                            : "") ||(promote.ingredients && promote.ingredients
                            ? promote.ingredients.length
                            : "")
                        }}
                        ingredient</span
                    >
                    <span
                      v-if="
                        (promote.ingredients &&
                          promote.ingredients[lang] &&
                          promote.ingredients[lang].length > 1)||( promote.ingredients && promote.ingredients && promote.ingredients.length > 1)
                      "
                        >{{
                          (promote.ingredients && promote.ingredients[lang]
                            ? promote.ingredients[lang].length
                            : "") ||(promote.ingredients && promote.ingredients ? promote.ingredients.length : "")

                        }}
                        ingredients</span
                    >
                      <span v-if="promote.numIngredients == 1">{{ (promote.numIngredients ? promote.numIngredients : '') ? (promote.numIngredients ? promote.numIngredients : '') : '' }} ingredient</span>
                      <span v-if="promote.numIngredients > 1">{{ (promote.numIngredients ? promote.numIngredients : '') ? (promote.numIngredients ? promote.numIngredients : '') : '' }} ingredients</span>
                  </div>
                </div>
							</td>
							<td>
								<div class="menu">
									<div
                    class="menu-container"
                    :class="{
                      'menu-selected': promote.dropDown,
                    }"
                    @click="displayOption(promote, true)"
                  >
                  <img
                    alt="menu"
                    class="table-edit-btn"
                    :src="getEditButtonSrc(promote)"
                    @click.stop="displayOption(promote, true)"
                  />
									</div>
									<div class="menu-box" v-if="promote.dropDown">
										<ul class="menu-list">
											<li @click="editRecipe(promote.isin)">{{ $t('BUTTONS.PREVIEW_BUTTON') }}</li>
											<li @click="unPromoteRecipeAsync(promote, index)">{{ $t('COMMON.UNPROMOTE') }}</li>
										</ul>
									</div>
								</div>
							</td>
						</tr>
          </draggable>
						</tbody>
					</table>
          </div>
          <div class="recipe-category" id="recipeCategory" v-if="!isPageLoading && !isUpdating">
            <div class="recipe-header-section">
              <div v-if="!isSearchExitEnable" class="recipe-header">
                <span v-if="(recipeForCategoriesTotal) > 1">{{recipeForCategoriesTotal}} Recipes in Category</span>
                <span v-if="(recipeForCategoriesTotal) <= 1">{{recipeForCategoriesTotal}} Recipe in Category</span>
              </div>
              <div v-if="isSearchExitEnable" class="recipe-header">
                <span>Search results</span>
              </div>
              <div v-if="!isSelectionEnabled" class="search-section" :class="{'disable-content': isDataLoading}">
                <div v-if="!isSearchExitEnable && recipeDataForCategories.length" class="btn-green-text btn-small">
                  <span  @click="selectProducts()">{{ $t('COMMON.SELECT')  }}</span>
                </div>
                <div class="search-box">
                  <input type="text" class="search-input-box" @click="dropDownClose()" autocomplete="off" placeholder="Search for recipe name" v-model.trim="queryRecipe" @keypress.enter="searchRecipeList()"
                    :class="{'align-search-input-box': queryRecipe}">
                  <img alt="" class="search-icon-green-image" @click="searchRecipeList()" src="@/assets/images/search-icon-green.png">
                  <img alt="" class="exit-search-icon" v-if="isSearchExitEnable" @click="resetQuery()" src="@/assets/images/exit-gray.png">
                </div>
                <div class="add-btn" @click="addRecipe()">
                  <img alt="" class="add-image" src="@/assets/images/category-add.png">
                  <span class="text">
                    {{$t('COMMON.ADD_RECIPE') }}
                  </span>
                </div>
              </div>
            </div>
            <div v-show="isSelectionEnabled" class="edit-category-selection-container">
              <div  class="edit-category-selection-panel">
                <div id="selectAllCheckboxId"  class="edit-category-select-all-checbox-section">
                  <label class="edit-category-checkbox-section">
                      <input type="checkbox" :checked="selectionOfRecipes[0].isSelected" @click="selectAllMatches()" >
                      <span class="checkmark"></span>
                  </label>
              </div>
              <div @click="selectAllMatches()" class="edit-category-select-all-text text-h3">Select All</div>
              <div class="edit-category-selection">
                <div class="edit-category-selected-text"> {{checkSelectedRecipes}} selected<span v-if="selectedProducts.length>0" class="edit-category-selected-cross-icon">
                  <img src="@/assets/images/close.svg?skipsvgo=true" @click="removeAllSelected()" alt="edit-category-close-icon">
              </span></div>
              </div>
              <div class="edit-category-btn-container">
                <button type="button" :class="selectedProducts.length == 0 ? 'disabled-button btn-red' : 'btn-red'" @click="deleteSelect()">{{  $t('BUTTONS.REMOVE_BUTTON')  }}</button>
              </div>
              <button type="button" class="edit-category-cancel-btn text-h3" @click="cancelSelect()">{{ $t('BUTTONS.CANCEL_BUTTON') }}</button>
              </div>
            </div>
            <div class="recipe-table-content">
              <div class="add-zero-section category-recipe-section" v-if="!isSearchExitEnable && recipeForCategoriesTotal == 0">
                <div class="zero-promoted">
                  <span class="bold text-title-2">
                    0 RECIPE IN CATEGORY.
                  </span>
                  <span class="normal text-title-2 font-normal">
                    Add recipes in category.
                  </span>
                </div>
              </div>
              <div class="no-result-for-category" v-if="recipeForCategoriesTotal == 0 && isSearchExitEnable && recipeDataForCategories ==''">
                {{ $t('COMMON.NO_RESULT_FOUND') }}
              </div>
              <div class="loader" v-if="isDataLoading">
                <loader />
              </div>
              <table class="recipe-table" id="recipeTable" v-if="recipeDataForCategories && !isDataLoading">
                <caption></caption>
                <thead class="table-head" v-show="recipeDataForCategories.length>0">
                  <tr class="title">
                    <th></th>
                    <th></th>
                    <th class="category-group-isin"><span>{{ $t('COMMON.RECIPE_ISIN') }} </span></th>
                    <th class="category-group-title"><span>{{ $t('COMMON.RECIPE_TITLE') }}</span></th>
                    <th class="category-group-total-time"><span> {{ $t('COMMON.TOTAL_TIME') }}</span></th>
                    <th class="ing-count"><span>{{ $t('COMMON.INGREDIENT_COUNT') }}</span></th>
                    <th class="ing-promote"></th>
                    <th></th>
                  </tr>
                </thead>
                <tbody :style="{ cursor: isSelectionEnabled ? 'pointer' : 'default' }">
                <tr @click="selectMatchToDelete(index,recipe)" :id="recipe.isdeleteSelected ? 'delete-selected':''" class="body" v-for="(recipe, index) in recipeDataForCategories" :key="index" v-show="!recipe.isSearched"
                  :class="{'recipe-selected-color-category' : isSelectionEnabled && recipe.isSelectedToDelete}">
                  <td>
                    <div v-if="isSelectionEnabled"  :class="isSelectionEnabled ?'edit-category-product-table-srno-checkbox':'edit-category-product-table-srno'">
                      <div id="selectAllCheckboxId"  class="edit-category-select-all-checbox-section">
                        <label class="edit-category-checkbox-section">
                            <input @click="selectMatchToDelete(index,recipe)" :checked="recipe.isSelectedToDelete"  type="checkbox" >
                            <span class="checkmark"></span>
                        </label>
                      </div>
                    </div>
                  </td>
                  <td  class="table-image-recipe">
                    <div class="image-recipe" v-if="recipe.media">
                        <img
                          alt=""
                          loading="eager"
                          v-if="recipe.media && recipe.media[lang] && recipe.media[lang].image && !recipe.media[lang].externalImageUrl"
                          class="image"
                          :src="recipe.media && recipe.media[lang] && recipe.media[lang].image ? recipe.media[lang].image : '' "
                        />
                        <img
                          alt=""
                          loading="eager"
                          v-if="recipe.media && recipe.media[lang] && recipe.media[lang].externalImageUrl"
                          class="image"
                          :src="recipe.media && recipe.media[lang] && recipe.media[lang].externalImageUrl ? recipe.media[lang].externalImageUrl : ''"
                          @error="$event.target.src=`${defaultImage}`"
                        />
                        <img
                          alt=""
                          loading="eager"
                          v-if="(!recipe.media || !recipe.media[lang] || !recipe.media[lang].image) && ( !recipe.media || !recipe.media[lang] || !recipe.media[lang].externalImageUrl)"
                          class="image"
                          :src="defaultImage"
                        />
                    </div>
                    <div class="image-recipe" v-if="recipe.image">
                      <img alt="" loading="eager" class="image" :src="recipe.image.url ? (recipe.image.url ? recipe.image.url : defaultImage) : '' ">
                    </div>
                    <div class="image-recipe" v-if="!recipe.image&&!recipe.media">
                      <img alt="" class="image" :src="defaultImage" loading="eager"/>
                    </div>
                  </td>
                  <td  class="table-recipe-code">
                  <div class="recipe-code">
                  {{ recipe.isin ? recipe.isin : '' }}
                  </div>
                  </td>
                  <td class="table-recipe-name">
                    <div
                      class="edit-category-recipe-name-tooltip"
                      :class="{
                        'simple-data-tooltip': isRecipTagInFocus,
                      }"
                      :data-tooltip-text="isRecipTagInFocus && (recipe?.title?.[lang] || recipe?.title)"
                    >
                      <div
                        class="recipe-name"
                        :id="`simplerecipename${index}`"
                        @mouseover="showRecipNameTip(index)"
                        @mouseleave="hideRecipenameTip(index)"
                      >
                        {{ recipe.title[lang] || recipe.title || '' }}
                      </div>
                      <div
                        v-if="recipe?.langs?.length > 1"
                        class="simple-data-tooltip"
                        :data-tooltip-text="getAvailableLanguagesTooltip(recipeDataForCategories)"
                      >
                        <img alt="" src="@/assets/images/language-icon.png">
                      </div>
                    </div>
                    <div class="recipe-subtitle" v-if="recipe && recipe.subtitle && recipe.subtitle[lang]">
                      <span v-if="recipe && recipe.subtitle && recipe.subtitle[lang].length >= 40">
                        {{ recipe && recipe.subtitle && recipe.subtitle[lang] ? recipe.subtitle[lang].substring(0, 40) + "..." : " "}}
                      </span>
                      <span v-else>
                        {{ recipe && recipe.subtitle && recipe.subtitle[lang] ? recipe.subtitle[lang]: " "}}
                      </span>
                    </div>
                  </td>
                  <td  class="table-recipe-time">
                                      <div class="recipe-details">
                      <div class="details">
                                    <span v-if="recipe.time && parseDurationString(recipe.time.total)">{{parseDurationString(recipe.time.total ? recipe.time.total : '') ? parseDurationString(recipe.time.total ? recipe.time.total : '') : ''}}</span>
                        <span v-if="recipe.totalTime && parseDurationString(recipe.totalTime)">{{parseDurationString(recipe.totalTime ? recipe.totalTime : '') ? parseDurationString(recipe.totalTime ? recipe.totalTime : '') : ''}}</span>
                        <span v-if="!(recipe.totalTime) && !(recipe.time && recipe.time.total)">none</span>
                      </div>
                                      </div>
                  </td>
                  <td>
                  <div class="recipe-details">
                  <div class="details">
                    <span
                      v-if="
                        recipe.ingredients &&
                          recipe.ingredients[lang] &&
                          recipe.ingredients[lang].length == 1
                      "
                      >{{
                        recipe.ingredients && recipe.ingredients[lang]
                          ? recipe.ingredients[lang].length
                          : ""
                      }}
                      ingredient</span
                    >
                    <span
                      v-if="
                        recipe.ingredients &&
                          recipe.ingredients[lang] &&
                          recipe.ingredients[lang].length > 1
                      "
                      >{{
                        recipe.ingredients && recipe.ingredients[lang]
                          ? recipe.ingredients[lang].length
                          : ""
                      }}
                      ingredients</span
                    >
                      <span v-if="recipe.numIngredients == 1">{{ (recipe.numIngredients ? recipe.numIngredients : '') ? (recipe.numIngredients ? recipe.numIngredients : '') : '' }} ingredient</span>
                      <span v-if="recipe.numIngredients > 1">{{ (recipe.numIngredients ? recipe.numIngredients : '') ? (recipe.numIngredients ? recipe.numIngredients : '') : '' }} ingredients</span>
                    </div>
                  </div>
                  </td>
                  <td :class="recipe.isPromoted ? 'disable-table-data' : ''" class="table-recipe-promote-btn">
                    <div
                      v-if="!isSelectionEnabled"
                      class="recipe-btn"
                      :class="{
                        'simple-data-tooltip': recipe?.status !== 'active',
                      }"
                      :data-tooltip-text="recipe?.status !== 'active' && $t('COMMON.CANNOT_PROMOTE_UNPUBLISHED_RECIPES')"
                    >
                      <button
                        type="button"
                        class="btn-green-outline"
                        :class="{ disabled: recipe?.status !== 'active' }"
                        @click="promoteRecipeAsync(recipe, index)"
                        @keydown="preventEnterAndSpaceKeyPress($event)"
                      >
                        {{ $t('COMMON.PROMOTE') }}
                      </button>
                    </div>
                  </td>
                  <td  v-if="!recipe.isPromoted" :class="recipe.isPromoted ? 'disable-table-data' : ''">
                    <div class="menu">
                      <div
                        v-if="!isSelectionEnabled"
                        class="menu-container"
                        :class="{
                           'menu-selected': recipe.dropDown
                        }"
                        @click="displayOption(recipe, false)"
                      >
                      <img
                        alt="menu"
                        class="table-edit-btn"
                        :src="getEditButtonSrc(recipe)"
                        @click.stop="displayOption(recipe, false)"
                      />
                      </div>
                      <div class="menu-box" v-if="recipe.dropDown && !isSelectionEnabled">
                        <ul class="menu-list">
                          <li @click="editRecipe(recipe.isin)">{{ $t('BUTTONS.PREVIEW_BUTTON') }}</li>
                          <li v-if="!isSelectionEnabled" @click="deleteRecipeListPopUp(recipe, index)">{{  $t('BUTTONS.REMOVE_BUTTON')  }}</li>
                        </ul>
                      </div>
                    </div>
                  </td>
                </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
        <paginate
          id="pagination-block"
          v-if="(recipeForCategoriesTotal>sizeRecipe) && !isUpdating"
          v-model="currentPage"
          :total-rows="recipeForCategoriesTotal"
          :page-range="pageRange"
          :per-page="sizeRecipe"
          :page-count=Math.ceil(recipeForCategoriesTotal/sizeRecipe)
          first-button-text="<<"
          prev-text="<"
          next-text=">"
          last-button-text=">>"
          :prev-class="'prev'"
          :next-class="'next'"
          :first-last-button=true
          :click-handler="pageChangeAsync"
          :container-class="'pagination'"
          :page-class="'page-item'"
          :page-link-class="'page-link'"
          :disabled-class="'disabled-pagination'"
          :active-class="'active'"
          :margin-pages="marginPages"
        >
        </paginate>
      </div>
        <deleteModal
          v-if="isRemoveCategoryVariantVisible"
          :closeModal="closeModal"
          :productInfoTitle="'Remove Category Variant?'"
          :productDescriptionOne="'Are you sure you want to remove this variant from the'"
          :productDescriptionTwo="$t('DESCRIPTION_POPUP.CATEGORY')"
          :deleteItem="removeCategoryVariant"
          :availableLanguage="0"
          :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
        />
        <unableToContentModal
          v-if="isUnableToPublishArticle"
            :closeModal="closeModal"
            :text="$t('TEXT_POPUP.NOT_PUBLISHED')"
        />
        <addRecipeModal
          v-if="isAddRecipeModal"
          @preventEnterAndSpaceKeyPress="preventEnterAndSpaceKeyPress"
          @closeModal="closeModal"
          :recipeDataForCategories="recipeDataForCategories"
          :categoryPromotedRecipes="categoryPromotedRecipes"
          @campaignModifiedAddRecipe="campaignModifiedAddRecipe"
          :selectedCategoryRecipe="selectedCategoryRecipe"
          :removeRecipeList="removeRecipeList"
          :recipesAfterPageChange="recipesAfterPageChange"
          :recipeMatchesIsinsRemove="recipeMatchesIsinsRemove"
          :isEditCategories="isEditCategories"
          @getRecipeDataForCategories="getRecipeDataForCategoriesAsync"
          :isPageLoading="isPageLoading"
        />
        <deleteModal
          v-if="isDeleteCategoryModal"
          :closeModal="closeModal"
          :productInfoTitle="'Delete Category?'"
          :productDescriptionOne="$t('DESCRIPTION_POPUP.DELETE_POPUP')"
          :productDescriptionTwo="$t('DESCRIPTION_POPUP.CATEGORY')"
          :deleteItem="deleteCategory"
          :availableLanguage="0"
          :buttonText="$t('BUTTONS.DELETE_BUTTON')"
        />
        <deleteModal
          v-if="isDeleteCategoryModalRecipe"
          :closeModal="closeModal"
          :productInfoTitle="$t('DESCRIPTION_POPUP.REMOVE_RECIPE')"
          :productDescriptionOne="$t('DESCRIPTION_POPUP.REMOVE_RECIPE_POPUP')"
          :productDescriptionTwo="$t('DESCRIPTION_POPUP.CATEGORY')"
          :deleteItem="removeRecipeAsync"
          :availableLanguage="0"
          :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
        />
      <saveModal v-if="isPublishModalVisible"
        :closeModal="closeModal"
        :saveAndPublishFunction="publishConfirm"
        :availableLang="[]"
        :buttonName="$t('BUTTONS.CONFIRM_BUTTON')"
        :description="$t('DESCRIPTION_POPUP.PUBLISH_POPUP')"
        :imageName="unpublishedImage"
      />
      <unpublishModal
        v-if="isUnPublishModalVisible"
        :description="'Do you want to unpublish this category?'"
        :noteMessage="isDisplayCategoryGroup ? 'Unpublishing this category will remove it from all associated category group(s) and the widget. Proceed?' : ''"
        :buttonName="$t('BUTTONS.CONFIRM_BUTTON')"
        :unpublishFunction="unPublishConfirm"
        :closeModal="closeModal"
      />
      <saveModal v-if="isSaveModalVisible && isCategoriesStatus != 'active'"
        :closeModal="closeModal"
        :saveAndPublishFunction="saveButtonClickAsync"
        :availableLang="[]"
        :isSlugCheckConfirm="isSlugCheckConfirm"
        :hasSlugExist="hasSlugExist"
        :buttonName="$t('BUTTONS.SAVE_BUTTON')"
        :description="$t('DESCRIPTION_POPUP.SAVE_UPDATES_POPUP')"
        :imageName="savePopupImage"
      />
      <saveModal v-if="isSaveModalVisible && isCategoriesStatus == 'active'"
        :closeModal="closeModal"
        :saveAndPublishFunction="saveButtonClickAsync"
        :availableLang="[]"
        :isSlugCheckConfirm="isSlugCheckConfirm"
        :hasSlugExist="hasSlugExist"
        :buttonName="'Publish'"
        :description="$t('DESCRIPTION_POPUP.PUBLISH_UPDATES_POPUP')"
        :imageName="publishVariantIcon"
      />
      <cancelModal
      v-if="isConfirmModalVisible"
      :availableLang="[]"
      :isCampaignModifiedFromShoppableReview="false"
      :callConfirm="backtoCategories"
      :closeModal="closeModal"
      />
      <Modal v-if="openPreviewRecipe" @close="closeModal">
              <template #noProductMatches>
                  <div class="open-preview-recipe-modal">
                      <div class="recipe-main-preview-header">
                          <div class="recipe-preview-header text-h2">{{ $t('COMMON.RECIPE_PREVIEW') }}</div>
                      </div>
                      <img alt=""
                          class="close-preview-recipe-modal"
                          @click="closeModal"
                          src="../assets/images/exit-gray.png"
                      />
                      <div class="open-preview-recipe-main">
                          <recipePreviewDetail
                              :rISIN="rISIN"
                              :checkRecipePreviewVideo="checkRecipePreviewVideo"
                          ></recipePreviewDetail>
                      </div>
                  </div>
              </template>
          </Modal>
          <selectTheLanguageModal
            v-if="hasRecipeVariantLanguagePopUp"
            :closeModal="closeModal"
            @preventEnterAndSpaceKeyPress="preventEnterAndSpaceKeyPress"
            @nextVariantPopUp="nextCategoryVariantNameModalPopUp"
            :recipeVariantLanguage="recipeVariantLanguage"
            @setRecipeVariantLanguageMatches="setRecipeVariantLanguageMatches"
            @showRecipeVariantLanguageMatches="showRecipeVariantLanguageMatches"
            :recipeVariantLanguageList="recipeVariantLanguageList"
            :hasRecipeVariantLanguageResult="hasRecipeVariantLanguageResult"
          />
      <savingModal
        v-show="isCategorySaving"
        :status="isCategoriesStatus == 'active' ? 'publishing' : 'saving'"
      />
      <addVariant
        v-if="isAddVariantCategoryNamePopUp"
        :closeModal="closeModal"
        :typeName="'Category'"
        :addVariantSelectedLanguage="recipeVariantSelectedLanguage"
        :itemName="categoriesName"
        @addConfirmVariant="addRecipeVariant"
        @preventEnterAndSpaceKeyPress="preventEnterAndSpaceKeyPress"
        @backToRoute="backToSelectLanguageVariantPopUp"
      />
      <invalidImageVideoPopup
      v-show="isInvalidImageModalVisible && !$nuxt.isOffline"
      :closeModal="closeModal"
      :acceptedFile="' jpg,png'"
      :video="false"
      :image="true"
      :zip="false"
      />
      <sizeLimit
      v-if="isUploadingImagePopup"
      :continueImage="continueImage"
      :maxFileSize="$t('DESCRIPTION_POPUP.LARGER_FILE')"
      :optimalImageSize="$t('DESCRIPTION_POPUP.OPTIMAL_IMAGE')"
      :closeModal="closeModal"
      :isUploadingImagePopup="isUploadingImagePopup"
      />
      <sizeLimit
      v-if="isMaxImagePopupVisible"
      :imageSizeAlert="$t('DESCRIPTION_POPUP.MAX_IMAGE_SIZE')"
      :fileSizeAlert="$t('DESCRIPTION_POPUP.MAX_IMAGE') "
      :closeModal="closeModal"
      :isMaxImagePopupVisible="isMaxImagePopupVisible"
      />
        <deletingModal v-show="isDeletingModalVisible" />
      <unableToUnpublish
        v-if="isDisplayWarningPopupConfirm"
        :title="'Unable to Unpublish. This category is associated to one or more category groups.'"
        :description="'Please remove all associations to unpublish in the Search filter page.'"
        :closeModal="closeModal"
      />
      <deleteModal
      v-if="isSelectDeleteModalVisible"
      :closeModal="closeModal"
      :productInfoTitle="$t('DESCRIPTION_POPUP.REMOVE_RECIPES')"
      :productDescriptionOne="$t('DESCRIPTION_POPUP.REMOVE_RECIPES_POPUP')"
      :productDescriptionTwo="$t('DESCRIPTION_POPUP.CATEGORY')"
      :deleteItem="deleteSelectProductMatches"
      :availableLanguage="0"
      :buttonText="$t('BUTTONS.REMOVE_BUTTON')"
    />
    </div>
    </content-wrapper>
  </client-only>
  </template>

  <script setup>
  import { ref, onMounted, computed, watchEffect} from 'vue';
  import ContentWrapper from "@/components/content-wrapper/content-wrapper";
  import unpublishModal from "@/components/unpublish-modal";
  import loader from "@/components/loader.vue";
  import savingModal from "@/components/saving-modal";
  import deletingModal from "@/components/deleting-modal";
  import invalidImageVideoPopup from "@/components/invalid-image-video-popup";
  import cancelModal from "@/components/cancel-modal";
  import saveModal from "@/components/save-modal";
  import sizeLimit from "@/components/size-limit.vue";
  import Modal from "@/components/Modal";
  import deleteModal from "@/components/delete-modal";
  import addVariant from "@/components/add-variant.vue";
  import selectTheLanguageModal from "@/components/select-the-language"
  import RecipeService from "@/services/RecipeService";
  import unableToContentModal from "@/components/unable-to-content-modal";
  import addRecipeModal from "@/components/add-recipe-modal.vue";
  import recipePreviewDetail from "@/components/recipe-preview-detail";
  import { debounce } from "lodash";
  import axios from "axios";
  import unableToUnpublish from "@/components/unableToUnpublish";
  import unpublishedImage from "../assets/images/unpublished.png";
  import savePopupImage from "../assets/images/1014367-MQuADjfW4ulIQ-en-US-0.png";
  import publishVariantIcon from "../assets/images/publish-variant-icon.png";
  import defaultImage from  "~/assets/images/default_recipe_image.png";
  import greenEditBtn from "../assets/images/green-edit-btn.svg?skipsvgo=true";
  import editButton from "../assets/images/edit-btn.svg?skipsvgo=true";
  import { useProjectLang } from "@/composables/useProjectLang";
  import { useCommonUtils } from "~/composables/useCommonUtils";
  import { useStore } from 'vuex';
  import { useRoute ,useRouter } from 'vue-router';
  import { useTimeUtils } from "@/composables/useTimeUtils";
  import { useEventUtils } from "@/composables/useEventUtils"
  import { useDelayTimer } from '~/composables/useDelayTimer';
  import { VueDraggableNext as draggable  } from 'vue-draggable-next';
  import { QUERY_PARAM_KEY } from "@/сonstants/query-param-key";

const isCheckVideoPreview = ref(false);
const isDataLoading = ref(false);
const hasSlugExist = ref(false);
const isSlugCheckConfirm = ref(false);
const isSlugInputWarning = ref(false);
const isUnableToPublishArticle = ref(false);
const hasSlugStatus = ref(false);
const isDeletingModalVisible = ref(false);
const isSomethingWrong = ref(false);
const finalAvailableLangs = ref([]);
const searchcopy = ref("");
const isSearchCheck = ref(false);
const hasRecipeTitleFocus = ref(false);
const isMaxImagePopupVisible = ref(false);
const uploadImageConfirm = ref("");
const isUploadingImagePopup = ref(false);
const isInvalidImageModalVisible = ref(false);
const isCategoryAlertIcon = ref(false);
const isRemoveCategoryVariantVisible = ref(false);
const isPageLoading = ref(false);
const isCategorySaving = ref(false);
const isDeleteCategoryModalRecipe = ref(false);
const isDeleteCategoryModal = ref(false);
const isAddRecipeModal = ref(false);
const categoriesName = ref("");
const categoriesSlug = ref("");
const openPreviewRecipe = ref(false);
const rISIN = ref("");
const isCategoriesStatus = ref("hidden");
const categoriesState = ref("");
const categoriesImage = ref("");
const editCategoriesISIN = ref("");
const fromRecipe = ref(0);
const marginPages = ref(0);
const sizeRecipe = ref(10);
const recipeForCategoriesTotal = ref(0);
const categoryPromotedCount = ref(0);
const recipeDataForCategories = ref([]);
const categoryPromotedRecipes = ref([]);
const filteredRecipeIsins = ref([]);
const queryRecipe = ref("");
const selectedCategoryRecipe = ref([]);
const currentPage = ref(1);
const queryText = ref("");
const recipeDataTotal = ref([]);
const operationStatusDetails = ref("");
const pageRange = ref(6);
const promotedRecipesIsins = ref([]);
const recipeMatchesIsinsRemove = ref([]);
const isSearchExitEnable = ref(false);
const removeRecipeData = ref({});
const removeRecipeIndex = ref(0);
const removeRecipeList = ref([]);
const isSaveModalVisible = ref(false);
const isPublishModalVisible = ref(false);
const isUnPublishModalVisible = ref(false);
const recipesAfterPageChange = ref([]);
const isCampaignModified = ref(false);
const isConfirmModalVisible = ref(false);
const dropdownItem = ref([]);
const unpromoteRemovedData = ref([]);
const hasRecipeVariantLanguagePopUp = ref(false);
const hasRecipeVariantLanguageResult = ref(false);
const recipeVariantLanguage = ref("");
const recipeVariantSelectedLanguage = ref("");
const recipeVariantLanguageList = ref([]);
const isAddVariantCategoryNamePopUp = ref(false);
const recipeVariantLanguageIndex = ref(0);
const variantName = ref("");
const recipeVariantList = ref([]);
const categoryVariantDataIndex = ref("");
const selectedDefaultLang = ref([]);
const deletedvariant = ref("");
const hasDisableSelectLanguageButton = ref(false);
const finalSelectedLanguage = ref([]);
const saveRemovedCategoryVariants = ref([]);
const initiallyVariantSupported = ref([]);
const isRecipeVariantNameEmpty = ref(false);
const isCategoryListTooltipVisible = ref(false);
const isRecipTagInFocus = ref(false);
const isUpdating = ref(false);
const file = ref("");
const imageResponseUrl = ref("");
const drag = ref(false)
const isAbortedCheckingOperationStatus = ref(false);
const isPageChange = ref(false);

const checktotal = ref(0);
const categoryAssociations = ref({});
const uploadImagePercentage = ref(0);
const isGlobeIconPresent = ref(false);
const loadedImageSize = ref(0);
const uploadImageSize = ref(0);
const image = ref("");
const hasDraggableTooltipDisplay = ref(false);
const isEditCategories = ref(false);
const isDisplayWarningPopup = ref(false);
const isDisplayWarningPopupConfirm = ref(false);
const isDisplayCategoryGroup = ref(true);
const showLoader = ref(false)

const selectionOfRecipes = ref([
  {
    isSelected: false,
  },
]);

const isSelectionEnabled = ref(false);
const selectedProducts = ref([]);
const isSelectDeleteModalVisible = ref(false);
const isAdminCheck = ref(false);
const lang = ref("");
const filesName = ref("");
const langVariantTooltip = "Some recipes have a language variant. Add the language variant of the category.";
const isCategoryIncludeInHero = ref("");


const store = useStore();
const {parseDurationString} = useTimeUtils();
const { getRef } = useRefUtils();
const { preventEnterAndSpaceKeyPress , restrictToAlphanumeric, onEscapeKeyPress } = useEventUtils()
const route = useRoute();
const router = useRouter();
const { $eventBus } = useNuxtApp();
const { triggerLoading , formatAndTrimSlug , generateIncrementedSlug , formatSlug , extractNumericCount, scrollToTop } = useCommonUtils();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { readyProject, isAdmin, getAvailableLangs } = useProjectLang();
const { delay } = useDelayTimer();

  onMounted(async () => {

    readyProject(async ({ isProjectReady }) => {
      if (isProjectReady) {
        isPageLoading.value = true;
        $eventBus.on("backButtonConfirm", () => {
          backToCategoriesConfirm();
        });
        $eventBus.on("closeModal", () => {
          closeModal();
        });
        $eventBus.on("getRecipeDataForCategories", () => {
          getRecipeDataForCategoriesAsync();
        });
        $eventBus.on("campaignModifiedAddRecipe", () => {
        campaignModifiedAddRecipe();
        });
        triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);

        isAdminCheck.value = isAdmin;
        lang.value = store.getters["userData/getDefaultLang"];
        finalAvailableLangs.value = await getAvailableLangs();

        isEditCategories.value = true;
        currentPage.value = 1;
        const queryParams = new URLSearchParams(window.location.search);
        isCategoryIncludeInHero.value = queryParams.get(QUERY_PARAM_KEY.IS_IN_HERO) === "true";

        await getCategoryDataAsync();
        await getEditCategoryListAsync();
        await getPromotedRecipesAsync();
        await pageChangeAsync(currentPage.value, false, false);
        await getCategoryAssociations();
        await getEditSearchAsync();

        handleAvailableLanguages();
        addEventListeners();

      }
    });

  });

  const checkSelectedRecipes = computed(() => {
    return selectedProducts.value.filter(data => data.isSelectedToDelete).length;
  });

  const hideEditcategorynameTip = () => {
    hasRecipeTitleFocus.value = false
  }

  const addEventListeners = () => {
    document.addEventListener("click", handleClickOutside);
    document.addEventListener("click", handleClickOutsidePopup);
    document.addEventListener("scroll", checkScrollPositon);
    document.addEventListener("input", handleClickNameInput);
  };

  const handleAvailableLanguages = () => {
    if (finalAvailableLangs.value?.length) {
      finalAvailableLangs.value.forEach((langVal) => {
        let langData = {};
        if (langVal === $keys.LANGUAGE.SPANISH) {
          langData = {
            language: langVal,
            language_name: $keys.LANGUAGE.SPANISH_LANGUAGE,
            languageFlag: "/images/flags/spain-flag.png",
          };
        } else if (langVal === $keys.LANGUAGE.FRENCH) {
          langData = {
            language: langVal,
            language_name: $keys.LANGUAGE.FRENCH_LANGUAGE,
            languageFlag: "/images/flags/france-flag.png",
          };
        }
        if (!selectedDefaultLang.value.includes(langVal)) {
          recipeVariantLanguageList.value.push(langData);
        }
      });
    }
  };

  const getEditButtonSrc = (recipe) => {
    return recipe.dropDown
      ? greenEditBtn
      : editButton;
  };

  const getImageSource = (promote) => {
    const media = promote?.media;
    return (
      media?.image ??
      media?.externalImageUrl ??
      media?.[lang.value]?.image ??
      media?.[lang.value]?.externalImageUrl ??
      defaultImage
    );
  };

  const isDeleteVariantVisible = (categoryVariant) => {
    const isCategoryAssociationTrue = categoryAssociations.value && categoryAssociations.value[categoryVariant.lang] > 0;
    const isRecipeVariantTrue = isGlobeIconPresent.value;

    if (isRecipeVariantTrue && !isCategoryAssociationTrue) {
      return true;
    } else if (!isCategoryAssociationTrue && !isRecipeVariantTrue) {
      return false;
    } else if (!isCategoryAssociationTrue || isRecipeVariantTrue) {
      return true;
    } else if (isCategoryAssociationTrue && !isRecipeVariantTrue) {
      return true;
    } else if (isCategoryAssociationTrue || !isRecipeVariantTrue) {
      return true;
    }
  };

  const removeAllSelected = () => {
    recipeDataForCategories.value.map((item) => {
      item.isSelectedToDelete = false;
    });
    selectionOfRecipes.value[0].isSelected = false;
    selectedProducts.value = [];
  };

  const selectAllMatches = () => {
    selectionOfRecipes.value[0].isSelected = !selectionOfRecipes.value[0].isSelected;

    if (selectionOfRecipes.value[0].isSelected) {
      recipeDataForCategories.value.map((item) => {
        item.isSelectedToDelete = true;
        selectedProducts.value.push(item);
      });
    } else {
      recipeDataForCategories.value.map((item) => {
        item.isSelectedToDelete = false;
        selectedProducts.value = selectedProducts.value.filter((insideItem) => insideItem.isin !== item.isin);
      });
    }

    selectedProducts.value = selectedProducts.value.filter(
      (value, index, self) => index === self.findIndex((t) => t.isin === value.isin)
    );
  };

  const selectMatchToDelete = (data, product) => {
    if (isSelectionEnabled.value) {
      recipeDataForCategories.value.map((item, index) => {
        if (index === data) {
          item.isSelectedToDelete = !item.isSelectedToDelete;
          if (item.isSelectedToDelete) {
            selectedProducts.value.push(item);
          } else {
            selectedProducts.value = selectedProducts.value.filter((insideItem) => insideItem.isin !== product.isin);
          }
        }
      });
      checkSelected();
    }
  };

  const deleteSelectProductMatches = () => {
    selectedProducts.value.forEach((data) => {
      if (data.isSelectedToDelete) {
        removeRecipeList.value.push(data);
        recipeMatchesIsinsRemove.value.push(data.isin);
        unpromoteRemovedData.value.push(data.isin);
      }
    });

    recipesAfterPageChange.value = recipesAfterPageChange.value.filter((data) => {
      return !recipeMatchesIsinsRemove.value.includes(data.isin);
    });

    isCampaignModified.value = true;
    currentPage.value = 1;
    pageChangeAsync(currentPage.value, false, false);
    closeModal();
    triggerLoading($keys.KEY_NAMES.DELETED);
    selectedProducts.value = [];
    selectionOfRecipes.value[0].isSelected = false;
    isSelectionEnabled.value = false;
  };
  const checkSelected = () => {
    let count = 0;
    recipeDataForCategories.value.forEach((item) => {
      if (item.isSelectedToDelete) {
        count += 1;
      }
    });
    selectionOfRecipes.value[0].isSelected = count === recipeDataForCategories.value.length;
  };

  const cancelSelect = () => {
    isSelectionEnabled.value = false;
    selectedProducts.value = [];
    if (recipeDataForCategories.value.length > 0) {
      selectionOfRecipes.value[0].isSelected = false;
      recipeDataForCategories.value.forEach((item) => {
        item.isSelectedToDelete = false;
      });
    }
    resetSelectbarPosition();
  };

  const selectProducts = () => {
    isSelectionEnabled.value = true;
  };

  const checkScrollPositon = () => {
    if (isSelectionEnabled.value) {
      const ele = document.querySelector("#recipeTable");
      if (
        ele.getBoundingClientRect().top < 0 &&
        isSelectionEnabled.value &&
        recipeDataForCategories.value.length >= 4
      ) {
        changeSelectbarPosition();
      } else {
        resetSelectbarPosition();
      }
    }
  };

  const resetSelectbarPosition = () => {
    const ele = document.querySelector(".edit-category-selection-container");
    const deletebtn = document.querySelector(".edit-category-btn-container");
    const cancelbtn = document.querySelector(".edit-category-cancel-btn");
    const selectText = document.querySelector(".edit-category-selected-text");
    const selectAll = document.querySelector(".edit-category-select-all-text");
    const selectBox = getRef("selectAllCheckboxId");
    const selectionPanel = document.querySelector(".edit-category-selection-panel");

    if (ele && deletebtn && cancelbtn && selectText && selectAll && selectBox && selectionPanel) {
      ele.style.position = "relative";
      ele.style.paddingTop = "0px";
      ele.style.top = "0px";
      ele.style.width = "calc(100% - 240px)";
      ele.style.boxShadow = "";
      ele.style.marginLeft = "0px";
      ele.style.height = "auto";
      deletebtn.style.right = "247px";
      cancelbtn.style.right = "20px";
      selectText.style.left = "136px";
      selectAll.style.left = "49px";
      selectBox.style.marginLeft = "9px";
      selectionPanel.style.marginTop = "15px";
    }
  };

  const changeSelectbarPosition = () => {
    const ele = document.querySelector(".edit-category-selection-container");
    const deletebtn = document.querySelector(".edit-category-btn-container");
    const cancelbtn = document.querySelector(".edit-category-cancel-btn");
    const selectText = document.querySelector(".edit-category-selected-text");
    const selectAll = document.querySelector(".edit-category-select-all-text");
    const selectBox = getRef("selectAllCheckboxId");
    const selectionPanel = document.querySelector(".edit-category-selection-panel");

    if (isSelectionEnabled.value && ele && deletebtn && cancelbtn && selectText && selectAll && selectBox && selectionPanel) {
      ele.style.backgroundColor = "#FFFFFF";
      ele.style.height = "64px";
      ele.style.alignItems = "center";
      ele.style.paddingTop = "inherit";
      ele.style.position = "fixed";
      ele.style.zIndex = "999";
      ele.style.top = "60px";
      ele.style.width = "-webkit-fill-available";
      ele.style.marginLeft = "-20px";
      ele.style.boxShadow = "1px 1px 4px 0px #888888";
      deletebtn.style.right = "300px";
      cancelbtn.style.right = "95px";
      selectText.style.left = "161px";
      selectAll.style.left = "74px";
      selectBox.style.marginLeft = "29px";
      selectionPanel.style.marginTop = "15px";
    }
  };

  const deleteSelect = () => {
    if (selectedProducts.value.length > 0) {
      isSelectDeleteModalVisible.value = true;
    }
    checktotal.value = 0;
    isGlobeIconPresent.value = checktotal.value !== 0;
  };

  const campaignModifiedAddRecipe = () => {
    isCampaignModified.value = true;
  };

  const backtoCategories = () => {
    backToCategoriesConfirm();
    closeModal();
  };

  const backToAddCategories = () => {
    router.push({ path: "/add-cat-group" });
  };

  const checkRecipePreviewVideo = (data) => {
    isCheckVideoPreview.value = !!data;
  };

  const uploadSameImageVideo = (event) => {
    event.target.value = "";
  };

  const checkUploadedFiles = (event) => {

    isCampaignModified.value = true;
    const evnt = event;
    file.value = evnt.target.files || evnt.srcElement.files;
    const fileType = file.value[0].type.split("/")[0];
    if (fileType === "image") {
      uploadFiles();
    } else {
      isInvalidImageModalVisible.value = true;
    }
  };

  const uploadImageFile = (url, file) => {
    const cancelImage = axios.CancelToken.source();
    axios
      .put(url, file, {
        headers: {
          "Content-Type": file.type,
          "x-amz-acl": "public-read",
        },
        cancelToken: cancelImage.token,
        onUploadProgress: (progressEvent) => {
          uploadImagePercentage.value = Math.round((progressEvent.loaded / progressEvent.total) * 100);
          uploadedImageFunctionAsync(uploadImagePercentage.value);
          loadedImageSize.value = progressEvent.loaded;
        },
      })
      .then(() => { })
      .catch((e) => {
        if (axios.isCancel(e)) {
          console.error("Image request canceled.");
        } else {
          console.error(e);
        }
      });
  };

  const uploadedImageFunctionAsync = async (data) => {
    if (data === 100) {
      uploadImagePercentage.value = 99;
      await delay(2000);
      uploadImagePercentage.value = 100;
    }
  };

  const checkSlugAsync = async (slug) => {
    isSlugCheckConfirm.value = true;
    showLoader.value = true;
    await store.dispatch("categories/checkCategorySlugExistAsync", { slug, lang: lang.value });
    const response = store.getters["categories/getCategorySlug"];
    if (response !== $keys.KEY_NAMES.SLUG_ALREADY_EXIST) {
      isSlugCheckConfirm.value = false;
      const isSlugDuplicate = response.isin !== editCategoriesISIN.value;
      hasSlugStatus.value = isSlugDuplicate;
      hasSlugExist.value = isSlugDuplicate;

      if (categoriesSlug.value !== "" && isSlugInputWarning.value) {
        isSlugInputWarning.value = false;

        let incrementSlug = categoriesSlug.value;
        const existedSlugCount = extractNumericCount(incrementSlug);
        const slugBase = formatSlug(incrementSlug);
        categoriesSlug.value = generateIncrementedSlug(slugBase, existedSlugCount);

        await checkSlugAsync(categoriesSlug.value);
        isSlugCheckConfirm.value = false;
      }
    } else {
      isSlugCheckConfirm.value = false;
      hasSlugExist.value = false;
      hasSlugStatus.value = false;
    }
  };

  const debounceInput = debounce(() => {
    callSlugChangeAsync();
  }, 2000);

  const checkEditCategorieName = () => {
    let name = getRef("title-name");
    if (
      name.scrollWidth > name.clientWidth &&
      categoriesName.value.trim().length > 0 &&
      name !== document.activeElement
    ) {
      hasRecipeTitleFocus.value = true;
    }
  };

  const checkPromoteName = (index) => {
    const element = getRef(`Promoterecipename${index}`);
    if (element.scrollHeight > element.clientHeight) {
      isCategoryListTooltipVisible.value = true;
    }
  };

  const hidePromoteNameTip = (index) => {
    const element = getRef(`Promoterecipename${index}`);
    if (element.scrollHeight > element.clientHeight) {
      isCategoryListTooltipVisible.value = false;
    }
  };

  const showRecipNameTip = (index) => {
    const element = getRef(`simplerecipename${index}`);
    if (element.scrollHeight > element.clientHeight) {
      isRecipTagInFocus.value = true;
    }
  };
  const hideRecipenameTip = (index) => {
    const element = getRef(`simplerecipename${index}`);
    if (element.scrollHeight > element.clientHeight) {
      isRecipTagInFocus.value = false;
    }
  };

  const getCategoryDataAsync = async () => {
    await store.dispatch('categories/getCategoryDataAsync', {
      lang: lang.value,
      isin: route.query.isin,
    });
    const response = store.getters['categories/getCategoryData'];
    isCategoryAlertIcon.value = response?.hasAlert ?? false;
  };

  const getCategoryAssociations = async () => {
    const promises = [];
    const variantList = [];

    recipeVariantList.value.forEach((lang) => {
      if (lang.lang !== lang.value) {
        promises.push(
          store.dispatch('categories/getCategoryAssociationsAsync', {
            isin: route.query.isin,
            from: 0,
            size: 15,
            lang: lang.lang,
          }).then(() => {
            const response = store.getters['categories/getCategoryAssociations'];
            const object = {
              [lang.lang]: response?.recipes?.length || response?.recipeGroups?.length || 0,
            };
            variantList.push(object);
          })
        );
      }
    });

    await Promise.all(promises);
    categoryAssociations.value = Object.assign({}, ...variantList);
  };

  const checkVariantNameEmpty = () => {
    if (recipeVariantList.value.length === 0) {
      isRecipeVariantNameEmpty.value = false;
      return isRecipeVariantNameEmpty.value;
    }

    const count = recipeVariantList.value.filter(data => data.name.trim().length === 0).length;

    isRecipeVariantNameEmpty.value = count > 0;
  };

  const displayTooltipLanguage = (item, index, langLength) => {
    const arr = item.split("-");
    if (item !== lang.value) {
      return index < langLength ? `${arr[0].toUpperCase()},` : `${arr[0].toUpperCase()}.`;
    }
  };

  const getAvailableLanguagesTooltip = (list) => {
    if (!list?.length) return "";

    const uniqueLanguages = [
      ...new Set(
        list.flatMap(recipe =>
          recipe.langs.map((item) => displayTooltipLanguage(item))
        )
      ),
    ];

    return `Available in ${uniqueLanguages.join(" ")}`;
  };
  const deleteCategoryListPopUp = () => {
    dropDownClose();
    isDeleteCategoryModal.value = true;
  };

  const deleteRecipeListPopUp = (recipe, index) => {
    dropDownClose();
    removeRecipeData.value = recipe;
    removeRecipeIndex.value = index;
    isDeleteCategoryModalRecipe.value = true;
    checktotal.value = recipeForCategoriesTotal.value - 1;
    isGlobeIconPresent.value = checktotal.value !== 0;
  };

  const resetSlugStatus = () => {
    isSlugCheckConfirm.value = false;
    hasSlugStatus.value = false;
    hasSlugExist.value = false;
  };
  const generateUniqueSlugAsync = async (slug) => {
    let incrementedSlug;
    let suffix = 1;
    while (true) {
      const newSlug = `${slug}-${suffix}`;
      await checkSlugAsync(newSlug);
      if (!hasSlugExist.value) {
        incrementedSlug = newSlug;
        break;
      }
      suffix++;
    }
    return incrementedSlug;
  };

  const processSlugAsync = async () => {
    const slugLength = 90;
    categoriesSlug.value = formatAndTrimSlug(categoriesSlug.value, slugLength);
    await checkSlugAsync(categoriesSlug.value);
  };

  const saveButtonClickAsync = async () => {
    isSlugCheckConfirm.value = true;

    if (hasSlugStatus.value) {
      isSlugInputWarning.value = true;

      if (categoriesSlug.value.trim() !== '') {
        await callSlugChangeAsync();

        if (hasSlugExist.value) {
          categoriesSlug.value = await generateUniqueSlugAsync(categoriesSlug.value);
        }

        await processSlugAsync();
        isSlugCheckConfirm.value = false;
        await saveButtonClickAsync();
      } else {
        resetSlugStatus();
      }
    } else {
      resetSlugStatus();
      isCategorySaving.value = true;

      await savePromotedRecipeAsync();

      if (recipeMatchesIsinsRemove.value.length > 0) {
        await removeCategoryRecipeAsync();

        if (isAbortedCheckingOperationStatus.value) {
          abortSaving();
          return;
        }
      }

      await postUpdateCategoriesConfirmAsync();
      isCampaignModified.value = false;
      closeModal();
    }
  };

  const abortSaving = () => {
    $eventBus.emit("show-floating-notification", {
      popupMessage: "Warning",
      popupSubMessage: "The category may not have been saved properly",
      popupType: $keys.KEY_NAMES.ERROR
    });
    isAbortedCheckingOperationStatus.value = false;
    isCategorySaving.value = false;
    closeModal();
  };

  const pageChangeAsync = async (event = currentPage.value, isScrollToTop = true, isPageChangeClicked = true) => {
    isPageChange.value = isPageChangeClicked;
    fromRecipe.value = (event - 1) * sizeRecipe.value;

    if (isScrollToTop) {
      scrollToTop();
    }

    updatePageUrl(event);
    await getRecipeDataForCategoriesAsync();
    selectionOfRecipes.value[0].isSelected = false;

    if (selectedProducts.value.length) {
      selectedProducts.value.forEach((item) => {
        recipeDataForCategories.value.forEach((data) => {
          if (item.isin === data.isin) {
            data.isSelectedToDelete = item.isSelectedToDelete;
          }
        });
      });
      checkSelected();
    }
  };

  const getEditCategoryListAsync = async () => {
    try {
      isPageLoading.value = true;
      triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);

      const isin = route.query.isin;
      if (!isin) return;

      const response = await fetchCategoryGroupData(isin);

      if (response) {
        processCategoryGroupData(response);
        updateCategoryDetails(response);
      }

    } catch (error) {
      console.error($keys.KEY_NAMES.ERROR_IN + "getEditCategoryListAsync:", error);
    } finally {
      finalizeLoading();
    }
  };

  const fetchCategoryGroupData = async (isin) => {
    try {
      await store.dispatch("categories/getEditCategoryGroupListAsync", {
        isin,
        lang: lang.value,
        sectionType: "category",
      });
      return store.getters["categories/getEditCategoryGroupList"];
    } catch (error) {
      console.error($keys.KEY_NAMES.ERROR_IN + "getEditCategoryData:", error);
      return null;
    }
  };

  const processCategoryGroupData = (response) => {
    const { data } = response;
    selectedDefaultLang.value = Object.keys(data);

    selectedDefaultLang.value.forEach((language) => {
      if (data?.[language] && language !== lang.value) {
        const newVariantData = {
          name: data[language].name,
          lang: language,
        };

        recipeVariantList.value.push(newVariantData);
        initiallyVariantSupported.value.push(newVariantData);
      }
    });
  };

  const updateCategoryDetails = (response) => {
    const { data, isin, status, state, slug } = response;


    editCategoriesISIN.value = isin || "";
    isCategoriesStatus.value = status || "";
    categoriesState.value = state || "";
    categoriesSlug.value = slug?.[lang.value] || "";

    const langData = data?.[lang.value];
    if (langData) {
      categoriesName.value = langData.name || "";
      categoriesImage.value = langData.image || defaultImage;

      if (categoriesImage.value) {
        image.value = categoriesImage.value;
        const categoryImage = document.getElementById("categoryImage");
        if (categoryImage) {
          categoryImage.style.backgroundImage = `url(${categoriesImage.value})`;
        }
      }
    }
  };

const finalizeLoading = () => {
  showLoader.value = false;
  isPageLoading.value = false;
};

const publishToggleBtnAsync = async () => {
  dropDownClose();
  if (
    isCategoriesStatus.value === "active" &&
    (categoriesState.value === "readyToPublish" ||
      categoriesState.value === "published")
  ) {
    if (isDisplayWarningPopup.value) {
      isUnPublishModalVisible.value = false;
      isDisplayWarningPopupConfirm.value = true;
    } else {
      isDisplayWarningPopupConfirm.value = false;
      isUnPublishModalVisible.value = true;
    }
  }
  if (
    isCategoriesStatus.value === "hidden" &&
    (categoriesState.value === "readyToPublish" ||
      categoriesState.value === "published")
  ) {
    isPublishModalVisible.value = true;
  }
};

const publishToggleBtnPopup = () => {
  if (isCategoriesStatus.value !== "active" &&
    (categoriesState.value !== "readyToPublish" ||
      categoriesState.value !== "published")) {
    isUnableToPublishArticle.value = true;
  } else {
    publishToggleBtnAsync();
  }
};

const getEditSearchAsync = async () => {
  await store.dispatch("editSearch/getEditSearchAsync");
  const response = store.getters["editSearch/getEditSearch"];

  if (response?.filters?.length) {
    const searchCategoryISINList = response.filters.find(data => data.type === "categories");

    if (searchCategoryISINList?.values?.length) {
      isDisplayWarningPopup.value = searchCategoryISINList.values.some(value => value.isin === editCategoriesISIN.value);
    }
  }
};

const patchPublishCategoryAsync = async () => {
  if (route.query.isin && isCategoriesStatus.value) {
    const payload = {
      status: isCategoriesStatus.value,
    };
    try {
      await store.dispatch("categories/patchCategoryAsync", {
        payload,
        isin: route.query[QUERY_PARAM_KEY.ISIN],
      });
      isCategorySaving.value = false;
      router.push({
        path: "/categories",
        query: {
          [QUERY_PARAM_KEY.SEARCH]: route.query[QUERY_PARAM_KEY.SEARCH],
        },
      });

      const loadingStatus = isCategoriesStatus.value !== 'active' ? "savedSuccess" : "isPublishedData";
      triggerLoading(loadingStatus);
    } catch (error) {
      showLoader.value = false;
      isCategorySaving.value = false;
    }
  }
};

const checkOperationStatusAsync = async (operationId) => {
  let timeout = 0;
  const states = [$keys.KEY_NAMES.DONE, $keys.KEY_NAMES.FAILED];
  const abortedTimer = setTimeout(() => isAbortedCheckingOperationStatus.value = true, 1000 * 60 * 2);
  while (true) {
    await new Promise((resolve) => setTimeout(resolve, timeout)); // Add delay to prevent excessive requests
    await getOperationStatusAsync(operationId);

    if (states.includes(operationStatusDetails.value.state) || isAbortedCheckingOperationStatus.value) {
      if (abortedTimer) {
        clearTimeout(abortedTimer);
      }

      operationStatusDetails.value = "";
      break;
    }

    timeout = 1000;
  }
}

const getOperationStatusAsync = async (operationId) => {
  operationStatusDetails.value = "";
  await store.dispatch("categories/getOperationStatusAsync", {
    operationId: operationId,
  });
  const response = store.getters["categories/getOperationStatus"];
  operationStatusDetails.value = response;
};
const deleteCategory = () => {
  postCategoryListAsync(editCategoriesISIN.value);
};

const closeModal = () => {
  isSelectDeleteModalVisible.value = false;
  isRemoveCategoryVariantVisible.value = false;
  isUnableToPublishArticle.value = false;
  isConfirmModalVisible.value = false;
  openPreviewRecipe.value = false;
  isDeleteCategoryModalRecipe.value = false;
  isDeleteCategoryModal.value = false;
  isAddRecipeModal.value = false;
  isSaveModalVisible.value = false;
  isPublishModalVisible.value = false;
  isUnPublishModalVisible.value = false;
  hasRecipeVariantLanguagePopUp.value = false;
  isAddVariantCategoryNamePopUp.value = false;
  hasDisableSelectLanguageButton.value = false;
  isInvalidImageModalVisible.value = false;
  isUploadingImagePopup.value = false;
  isMaxImagePopupVisible.value = false;
  variantName.value = "";
  isDeletingModalVisible.value = false;
  isSomethingWrong.value = false;
  isDisplayWarningPopupConfirm.value = false;
};


const postCategoryGroupListAsync = async (isin) => {
  const payload = {
    sourceId: $keys.KEY_NAMES.SOURCE_ID,
    data: {
      entityType: 'recipeCategoryGroup',
      action: 'removeAll',
      isin: isin,
    },
  };

  try {
    const response = await store.dispatch('categories/postCategoryRecipeAsync', { payload });
    const deleteCategoryOperationId = response.opId;
    await checkOperationStatusAsync(deleteCategoryOperationId);
    await deleteCategoryListAsync(isin);
    deleteISIN.value = '';
  } catch (error) {
    showLoader.value = false;
  }
};

const deleteCategoryListAsync = async (isin) => {
  if (route.query.isin) {
    try {
      await store.dispatch('categories/deleteCategoryList', { isin });
      triggerLoading('newDeletedSuccess');
      await router.push({ path: '/categories' });
      isDeletingModalVisible.value = false;
    } catch (error) {
      showLoader.value = false;
      isDeletingModalVisible.value = false;
      isSomethingWrong.value = true;
      await router.push({ path: '/categories' });
    }
  }
};

const searchRecipeList = () => {
  isSearchCheck.value = true;
  dropDownClose();
  queryText.value = queryRecipe.value;
  pageChangeAsync(currentPage.value, false, false);
  checktotal.value = recipeForCategoriesTotal;
};

const resetQuery = () => {
  checktotal.value = 0;
  isSearchCheck.value = false;
  queryRecipe.value = '';
  searchRecipeList();
};

const getPromotedRecipesAsync = async () => {
  isPageLoading.value = true;
  triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);

  if (route.query.isin) {
    try {
      await store.dispatch('categories/getPromotedRecipesForCategoriesAsync', {
        isin: route.query.isin,
        lang: lang.value
      });
      const response = store.getters['categories/getPromotedRecipesForCategories'];

      categoryPromotedRecipes.value = response?.promotedRecipes || [];
      categoryPromotedCount.value = categoryPromotedRecipes.value.length;

      categoryPromotedRecipes.value.forEach(data => {
        data.dropDown = false;
        data.isSelected = true;
        data.isSearched = false;
      });

      response.filteredRecipesIsins.forEach(item => {
        categoryPromotedRecipes.value.forEach(data => {
          if (data.isin === item) {
            data.isSelected = false;
          }
        });
      });
      promotedRecipesData(response);
      isPageLoading.value = false;
      triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
    } catch {
      showLoader.value = false;
    }
  }
};

const promotedRecipesData = (response) => {
  recipeStatus.value = response.preview;
  categoryPromotedRecipes.value = response.promotedRecipes || [];
  filteredRecipeIsins.value = response.filteredRecipesIsins || [];
};

const setPayLoadWithVariantAsync = async (payload) => {
  if (payload.image && payload.image[lang.value]) {
    payload.image = await setLanguageVariant(payload.image);
  }
  return payload;
};

const setPayLoadWithVariantSlugAsync = async (payload) => {
  if (payload.slug && payload.slug[lang.value]) {
    payload.slug = await setLanguageVariant(payload.slug);
  }
  return payload;
};

const postUpdateCategoriesConfirmAsync = async () => {
  let defaultVariantData = {
    name: categoriesName.value.trim(),
    lang: lang.value,
  };
  recipeVariantList.value.push(defaultVariantData);
  await postUpdateCategoriesAsync();
};

const postUpdateCategoriesAsync = async () => {

  if (route.query.isin) {
    let payload = {
      isin: route.query.isin,
      slug: {
        [lang.value]: categoriesSlug.value && categoriesSlug.value.trim()
          ? categoriesSlug.value.trim()
          : '',
      },
      type: 'category',
      data: updatedRecipeVariantList(recipeVariantList.value),
      image: {
        [lang.value]: imageResponseUrl.value
          ? imageResponseUrl.value.replace(/\?.*/, '')
          : categoriesImage.value,
      },
    };
    payload = await setPayLoadWithVariantAsync(payload);
    payload = await setPayLoadWithVariantSlugAsync(payload);
    if (payload && payload.slug && payload.slug[lang.value] === '') {
      delete payload.slug;
    }

    await store.dispatch('categories/postCategoryOrCategoryGroupAsync', {
      payload,
      lang: lang.value,
    }).then(async () => {
      if (saveRemovedCategoryVariants.value?.length) {
        await deleteVariantAsync();
      }
      if (selectedCategoryRecipe.value?.length) {
        await postCategoryRecipeAsync();

        if (isAbortedCheckingOperationStatus.value) {
          abortSaving();
          return;
        }
      }
      await patchPublishCategoryAsync();
    }).catch(() => {
      showLoader.value = false;
      isCategorySaving.value = false;
    });
  }
};

  const deleteVariantAsync = async () => {
    try {
      await store.dispatch('categories/deleteLanguageVariantAsync', {
        isin: route.query.isin,
        lang: saveRemovedCategoryVariants.value,
      });
    } catch (e) {
      console.error(e);
      showLoader.value = false;
    }
  };


  const uploadImageAsync = async () => {
    if (file.value) {
      const reader = new FileReader();
      reader.onload = async () => {
        const extension = file.value[0].type.split('/')[1];
        const params = {
          entity: 'recipeCategory',
          content: 'image',
          lang: "en-US",
          extension: extension,
          public: true,
        };
        if (route.query.isin) {
          await store.dispatch('preSignedUrl/getPreSignedImageUrlAsync', {
            isin: route.query.isin,
            params,
          });
          const response = store.getters['preSignedUrl/getPreSignedUrl'];
          await uploadImageFile(response?.data?.url ?? '', file.value[0]);
          await RecipeService.upload(response?.data?.url ?? '', file.value[0]);
          imageResponseUrl.value = response?.data?.url ?? '';
        }
      };
      if (file.value[0]) {
        reader.readAsDataURL(file.value[0]);
      }
    }
  };

  const continueImage = async () => {
    file.value = uploadImageConfirm.value;
    const reader = new FileReader();
    reader.onload = async () => {
      image.value = reader.result;
      if (image.value) {
        loadedImageSize.value = 0;
        uploadImagePercentage.value = 1;
        await uploadImageAsync();
      }
    };
    if (file.value[0]) {
      reader.readAsDataURL(file.value[0]);
    }
  };

  const uploadFiles = async () => {
    isCampaignModified.value = true;
    if (file.value.length > 0) {
      filesName.value = file.value[0].name.toLowerCase();
      const reg = /(.*?)\.(jpg|png|jpeg)$/;
      if (!filesName.value.match(reg)) {
        isInvalidImageModalVisible.value = true;
        file.value = '';
        filesName.value = '';
        return;
      }
      const fileSize = file.value[0].size;
      uploadImageSize.value = fileSize;
      const size = parseInt(fileSize.toFixed(0));
      if (size >= 1 * 1024 * 1024) {
        let element = getRef('productVideo');
        element.blur();
        isUploadingImagePopup.value = true;
        uploadImageConfirm.value = file.value;
        file.value = '';
      }
      if (size >= 15 * 1024 * 1024) {
        file.value = '';
        isUploadingImagePopup.value = false;
        isMaxImagePopupVisible.value = true;
        let element = getRef('productVideo');
        element.blur();
        return;
      } else {
        const reader = new FileReader();
        reader.onload = async () => {
          image.value = reader.result;
          if (image.value) {
            loadedImageSize.value = 0;
            uploadImagePercentage.value = 1;
            await uploadImageAsync();
          }
        };
        if (file.value[0]) {
          reader.readAsDataURL(file.value[0]);
        }
      }
    }
  };

  const getRecipeDataForCategoriesAsync = async () => {
    if (!route.query.isin) {
      console.warn('Missing required parameters');
      return;
    }


    if (isSearchCheck.value) {
      searchcopy.value = queryRecipe.value.trim();
    }

    const promotedIsins = extractIsins(categoryPromotedRecipes.value);
    const removedIsins = extractIsins(removeRecipeList.value);
    const addedIsins = extractIsins(recipesAfterPageChange.value);
    const filteredRemovedIsins = removedIsins?.filter(isin => !addedIsins?.includes(isin));
    const totalPromotedRemovedIsin = searchcopy.value.trim()
      ? [...removedIsins]
      : [...promotedIsins, ...filteredRemovedIsins];

    const payload = {
      country: lang.value.split('-')[1],
      q: searchcopy.value.trim(),
      excludingIsins: totalPromotedRemovedIsin.join(','),
      groupsIncludingIsins: addedIsins.join(','),
      groups: route.query.isin,
      from: fromRecipe.value,
      size: sizeRecipe.value,
      sort: 'lastMod',
    };

    try {
      isDataLoading.value = isPageChange.value;
      await store.dispatch('categories/getRecipeForCategoriesAsync', { payload });

      const response = store.getters['categories/getRecipeForCategories'];
      handleRecipeDataResponse(response);
    } catch (error) {
      console.error('Error while fetching recipe data:', error);
      handleRecipeDataError();
    } finally {
      isDataLoading.value = false;
      isPageChange.value = false;
    }
  };


  const extractIsins = (list) => {
    return list
      .filter(item => item?.isin)
      .map(item => item.isin);
  };

  const handleRecipeDataResponse = (response) => {
    isUpdating.value = false;
    isSearchCheck.value = false;
    recipeDataTotal.value = response.results || [];
    recipeDataForCategories.value = response.results || [];

    response?.results.forEach(data => {
      Object.assign(data, {
        dropDown: false,
        isSelected: !filteredRecipeIsins.value.includes(data.isin),
        isSearched: false,
        isSelectedToDelete: false,
        isPromoted: categoryPromotedRecipes.value.some(item => item.isin === data.isin),
      });
    });

    isGlobeIconPresent.value = recipeDataTotal.value.some(data => data?.langs?.length > 1);
    recipeForCategoriesTotal.value = response.total || 0;
    isSearchExitEnable.value = searchcopy.value.trim();
    isPageLoading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  };

  const handleRecipeDataError = () => {
    isUpdating.value = false;
    isSearchExitEnable.value = searchcopy.value.trim();
    isPageLoading.value = false;
    triggerLoading($keys.KEY_NAMES.ROUTE_LOADING, isPageLoading.value);
  };

  const totalIngredients = (tasks) => {
    let totalCount = 0;
    let ingredients = [];
    tasks.forEach((item) => {
      item[lang].ingredients.forEach((data) => {
        ingredients.push(data.foodItem);
      });
    });
    let uniqueIsins = [...new Set(ingredients)];
    totalCount = uniqueIsins.length;
    return totalCount;
  };

  const displayOption = (item, isPromotedOptions) => {
    dropdownItem.value = { ...item, dropDown: !item.dropDown };
    if (categoryPromotedRecipes.value && isPromotedOptions) {
      categoryPromotedRecipes.value = categoryPromotedRecipes.value.map((data) => {
        return data.isin !== item.isin ? { ...data, dropDown: false } : dropdownItem.value;
      });
    }
    if (recipeDataForCategories.value && !isPromotedOptions) {
      recipeDataForCategories.value = recipeDataForCategories.value.map((data) => {
        return data.isin !== item.isin ? { ...data, dropDown: false } : dropdownItem.value;
      });
    }
  };

  const addRecipe = () => {
    dropDownClose();
    resetQuery();
    isAddRecipeModal.value = true;
  };

  const backToCategories = () => {
    recipeDataForCategories.value.forEach((data) => {
      data.dropDown = false;
    });
    categoryPromotedRecipes.value.forEach((data) => {
      data.dropDown = false;
    });

    if (isCampaignModified.value) {
      isConfirmModalVisible.value = true;
    } else {
      route.query.backFrom === "add-cat-group"
        ? backToAddCategories()
        : backToCategoriesConfirm();
    }
  };

  const backToCategoriesConfirm = () => {
    dropDownClose();
    if (route.query[QUERY_PARAM_KEY.BACK_FROM] === "add-cat-group") {
      backToAddCategories();
      return;
    }

    if (route.query[QUERY_PARAM_KEY.FROM]) {
      router.push({
        path: "/edit-cat-group",
        query: {
          [QUERY_PARAM_KEY.ISIN]: route.query[QUERY_PARAM_KEY.FROM],
          [QUERY_PARAM_KEY.BACK_FROM]: route.query[QUERY_PARAM_KEY.BACK_FROM],
        },
      });
    } else {
      router.push({
        path: "/categories",
        query: {
          [QUERY_PARAM_KEY.PAGE]: route.query[QUERY_PARAM_KEY.BACK_FROM],
          [QUERY_PARAM_KEY.SEARCH]: route.query[QUERY_PARAM_KEY.SEARCH] || undefined,
        },
      });
    }
    isCampaignModified.value = false;
  };

  const promoteRecipeAsync = async (recipe, index) => {
    dropDownClose();
    isUpdating.value = true;
    recipe.dropDown = false;
    recipeForCategoriesTotal.value -= 1;
    isCampaignModified.value = true;
    categoryPromotedRecipes.value.push(recipe);
    promotedRecipesIsins.value.push(recipe.isin);
    unpromoteRemovedData.value.push(recipe.isin);
    recipeDataForCategories.value.splice(index, 1);

    if (recipeDataForCategories.value.length === 0 && currentPage.value > 1) {
      currentPage.value -= 1;
      await pageChangeAsync(currentPage.value, false, false);
    }

    if (recipesAfterPageChange.value.length > 0) {
      const recipeIndex = recipesAfterPageChange.value.findIndex(data => data.isin === recipe.isin);
      if (recipeIndex !== -1) {
        recipesAfterPageChange.value.splice(recipeIndex, 1);
      }
    }

    await getRecipeDataForCategoriesAsync();
    triggerLoading($keys.KEY_NAMES.RECIPE_PROMOTED);
  };

  const unPromoteRecipeAsync = async (recipe, index) => {
    recipe.dropDown = false;
    isUpdating.value = true;
    isCampaignModified.value = true;
    recipeForCategoriesTotal.value += 1;
    categoryPromotedRecipes.value.splice(index, 1);
    triggerLoading($keys.KEY_NAMES.RECIPE_UNPROMOTED);

    const recipeIndex = promotedRecipesIsins.value.indexOf(recipe.isin);
    if (recipeIndex !== -1) {
      promotedRecipesIsins.value.splice(recipeIndex, 1);
    }

    const unpromoteIndex = unpromoteRemovedData.value.indexOf(recipe.isin);
    if (unpromoteIndex !== -1) {
      unpromoteRemovedData.value.splice(unpromoteIndex, 1);
    }

    recipesAfterPageChange.value.push(recipe);
    if (isSearchExitEnable.value) {
      resetQuery();
    } else {
      await getRecipeDataForCategoriesAsync();
    }
  };

  const savePromotedRecipeAsync = async () => {
    const promotedData = categoryPromotedRecipes.value
      ? categoryPromotedRecipes.value.map((recipe) => recipe.isin)
      : [];

    const payload = {
      isin: route.query.isin,
      targetIsin: route.query.isin,
      campaignType: "categoryRecipeSuggestion",
      promotedRecipeIsins: [...new Set(promotedData)],
      filteredRecipeIsins: filteredRecipeIsins.value,
      preview: false,
    };

    try {
      await store.dispatch("categories/saveRecipeCampaignDataAsync", { payload, lang: lang.value });
      isSaveModalVisible.value = false;
      isPublishModalVisible.value = false;
    } catch {
      showLoader.value = false;
    }
  };

  const postCategoryRecipeAsync = async () => {
    const isin = route.query.isin;
    const payload = {
      sourceId: $keys.KEY_NAMES.SOURCE_ID,
      data: {
        action: "add",
        isin: isin,
        targets: selectedCategoryRecipe.value || [],
      },
    };

    try {
      const response = await store.dispatch("categories/postCategoryRecipeAsync", { payload });
      const operationId = response.opId;
      await checkOperationStatusAsync(operationId);
    } catch {
      showLoader.value = false;
    }
  };

  const removeCategoryRecipeAsync = async () => {
    const isin = route.query.isin;
    const payload = {
      sourceId: $keys.KEY_NAMES.SOURCE_ID,
      data: {
        action: "remove",
        isin: isin,
        targets: recipeMatchesIsinsRemove.value,
      },
    };

    try {
      const response = await store.dispatch("categories/postCategoryRecipeAsync", { payload });
      const operationId = response.opId;
      await checkOperationStatusAsync(operationId);
    } catch {
      showLoader.value = false;
    }
  };

  const removeRecipeAsync = async () => {
    if (recipeMatchesIsinsRemove.value) {
      removeRecipeList.value.push(removeRecipeData.value);
      recipeMatchesIsinsRemove.value.push(removeRecipeData.value.isin);
    }

    const recipesAfterPageChangeIndex = recipesAfterPageChange.value.findIndex(
      (data) => data.isin === removeRecipeData.value.isin
    );

    if (recipesAfterPageChange.value.length > 0 && recipesAfterPageChangeIndex > -1) {
      recipesAfterPageChange.value.splice(recipesAfterPageChangeIndex, 1);
    }

    removeRecipeData.value = {};
    isCampaignModified.value = true;
    isDeleteCategoryModalRecipe.value = false;

    if (recipeDataForCategories.value.length === 1 && currentPage.value !== 1) {
      currentPage.value -= 1;
      await pageChangeAsync(currentPage.value, false, false);
    } else {
      await getRecipeDataForCategoriesAsync();
    }

    closeModal();
    triggerLoading($keys.KEY_NAMES.DELETED);
  };

  const editRecipe = (isin) => {
    if (isin) {
      rISIN.value = isin;
      openPreviewRecipe.value = true;
    }
  };

  const moveRecipeUp = (item, index) => {
    dropDownClose();
    isCampaignModified.value = true;
    categoryPromotedRecipes.value.splice(index, 1);
    categoryPromotedRecipes.value.splice(index - 1, 0, item);
  };

  const updatePageUrl = (pageNo) => {
    let isin = route.query[QUERY_PARAM_KEY.ISIN];
    let from = route.query[QUERY_PARAM_KEY.FROM];
    let backFrom = route.query[QUERY_PARAM_KEY.BACK_FROM];
    if (
      route.query[QUERY_PARAM_KEY.SEARCH] !== "" &&
      route.query[QUERY_PARAM_KEY.SEARCH]
    ) {
      router.push({
        path: "edit-categories",
        query: {
          [QUERY_PARAM_KEY.ISIN]: isin,
          [QUERY_PARAM_KEY.BACK_FROM]: backFrom,
          [QUERY_PARAM_KEY.SEARCH]: route.query[QUERY_PARAM_KEY.SEARCH],
        },
      });
    } else if (!route.query[QUERY_PARAM_KEY.FROM]) {
      if (pageNo > 1) {
        router.replace({
          path: "edit-categories",
          query: {
            [QUERY_PARAM_KEY.ISIN]: isin,
            [QUERY_PARAM_KEY.BACK_FROM]: backFrom,
            [QUERY_PARAM_KEY.PAGE]: pageNo
          },
        });
      } else {
        router.replace({
          path: "edit-categories",
          query: {
            [QUERY_PARAM_KEY.ISIN]: isin,
            [QUERY_PARAM_KEY.BACK_FROM]: backFrom
          },
        });
      }
    } else if (pageNo > 1) {
      router.replace({
        path: "edit-categories",
        query: {
          [QUERY_PARAM_KEY.FROM]: from,
          [QUERY_PARAM_KEY.ISIN]: isin,
          [QUERY_PARAM_KEY.BACK_FROM]: backFrom,
          [QUERY_PARAM_KEY.PAGE]: pageNo
        },
      });
    } else {
      router.replace({
        path: "edit-categories",
        query: {
          [QUERY_PARAM_KEY.FROM]: from,
          [QUERY_PARAM_KEY.ISIN]: isin,
          [QUERY_PARAM_KEY.BACK_FROM]: backFrom
        },
      });
    }
    window.history.replaceState(null, null, window.location.href);
  };

  const displayPopupAsync = async () => {
    isSlugCheckConfirm.value = true;

    if (categoriesSlug.value.trim() !== "") {
      await processSlugAsync();
      isSlugCheckConfirm.value = false;
    } else {
      resetSlugStatus();
    }

    recipeDataForCategories.value.forEach((data) => {
      data.dropDown = false;
    });

    categoryPromotedRecipes.value.forEach((data) => {
      data.dropDown = false;
    });

    isSaveModalVisible.value = true;
  };

  const callSlugChangeAsync = async () => {
    isSlugInputWarning.value = false;
    isSlugCheckConfirm.value = true;

    if (categoriesSlug.value.trim() !== "") {
      await processSlugAsync();
      isSlugCheckConfirm.value = false;
    } else {
      resetSlugStatus();
    }
  };

  const postCategoryListAsync = async (isin) => {
    isCampaignModified.value = false;
    const payload = {
      sourceId: $keys.KEY_NAMES.SOURCE_ID,
      data: {
        entityType: "recipe",
        action: "removeAll",
        isin: isin,
      },
    };

    closeModal();
    isDeletingModalVisible.value = true;

    try {
      const response = await store.dispatch('categories/postCategoryRecipeAsync', { payload });
      const deleteCategoryOperationId = response.opId;
      await checkOperationStatusAsync(deleteCategoryOperationId);

      if (isAbortedCheckingOperationStatus.value) {
        abortPostingCategoryList();
        return;
      }

      await postCategoryGroupListAsync(isin);
    } catch (error) {
      showLoader.value = false;
      isDeletingModalVisible.value = false;
      isSomethingWrong.value = true;
    }
  };

  const abortPostingCategoryList = () => {
    $eventBus.emit("show-floating-notification", {
      popupMessage: "Warning",
      popupSubMessage: "The category may not have been deleted successfully",
      popupType: $keys.KEY_NAMES.ERROR
    });
    isAbortedCheckingOperationStatus.value = false;
    showLoader.value = false;
    closeModal();
  }

  const unPublishConfirm = () => {
    isCampaignModified.value = true;
    isCategoriesStatus.value = "hidden";
    closeModal();
  };

  const publishConfirm = () => {
    isCampaignModified.value = true;
    isCategoriesStatus.value = "active";
    closeModal();
  };

  const handleClickNameInput = (event) => {
    if (getRef("title-name")?.contains(event.target)) {
      isCampaignModified.value = true;
    }
  };

  const slugInput = () => {
    isCampaignModified.value = true;
    isSlugCheckConfirm.value = true;
  };

  const dropDownClose = () => {
    recipeDataForCategories.value.forEach((data) => {
      data.dropDown = false;
    });
    categoryPromotedRecipes.value.forEach((data) => {
      data.dropDown = false;
    });
  };

  const handleClickOutside = (event) => {
    if (dropdownItem.value?.dropDown) {
      const menuSelected = document.querySelector(".menu-selected");
      if (!menuSelected?.contains(event.target)) {
        dropdownItem.value.dropDown = false;
      }
    }
  };

  const handleClickOutsidePopup = (event) => {
    if (hasRecipeVariantLanguageResult.value) {
      if (!document.querySelector(".category-group-dropdown").contains(event.target)) {
        hasRecipeVariantLanguageResult.value = false;
      }
    }
  };

  const openRecipeVariantPopUp = () => {
    hasRecipeVariantLanguagePopUp.value = true;
    hasRecipeVariantLanguageResult.value = false;
    hasDisableSelectLanguageButton.value = false;
    recipeVariantLanguage.value = "";
  };

  const setRecipeVariantLanguageMatches = (value, index) => {
    recipeVariantLanguage.value = value.language;
    recipeVariantLanguageIndex.value = index;
    hasRecipeVariantLanguageResult.value = false;
    hasDisableSelectLanguageButton.value = true;

    recipeVariantLanguageList.value.forEach((data, idx) => {
      if (data.language === recipeVariantLanguage.value) {
        recipeVariantLanguageList.value.splice(idx, 1);
        recipeVariantLanguageList.value.unshift(data);
      }
    });
  };

  const nextCategoryVariantNameModalPopUp = (item) => {
    if (item === "") {
      recipeVariantSelectedLanguage.value = recipeVariantLanguageList.value[0]?.language || "";
      recipeVariantLanguage.value = recipeVariantLanguageList.value[0]?.language || "";
    } else {
      recipeVariantSelectedLanguage.value = item;
    }
    hasRecipeVariantLanguagePopUp.value = false;
    isAddVariantCategoryNamePopUp.value = true;
    hasRecipeVariantLanguageResult.value = false;
  };

  const showRecipeVariantLanguageMatches = () => {
    hasRecipeVariantLanguageResult.value = !hasRecipeVariantLanguageResult.value;
  };

  const backToSelectLanguageVariantPopUp = () => {
    isAddVariantCategoryNamePopUp.value = false;
    hasRecipeVariantLanguagePopUp.value = true;
  };

  const addRecipeVariant = (item) => {
    variantName.value = item;
    if (variantName.value !== "") {
      const newVariantData = {
        name: item.trim(),
        lang: recipeVariantLanguage.value,
      };
      recipeVariantList.value.push(newVariantData);
      isCampaignModified.value = true;
      isAddVariantCategoryNamePopUp.value = false;
      variantName.value = "";

      recipeVariantLanguageList.value = recipeVariantLanguageList.value.filter((data) => data.language !== recipeVariantLanguage.value);

      saveRemovedCategoryVariants.value = saveRemovedCategoryVariants.value.filter((data) => data !== recipeVariantLanguage.value);

      getCategoryAssociations();
    }
  };

  const displayLanguageCode = (item) => {
    if (item) {
      const arr = item.split("-");
      return arr[0].toUpperCase();
    }
    return "";
  };

  const deleteCategoryVariant = (data, index) => {
    isRemoveCategoryVariantVisible.value = true;
    categoryVariantDataIndex.value = index;
    deletedvariant.value = data.lang;
  };

  const removeCategoryVariant = () => {
    isCampaignModified.value = true;
    initiallyVariantSupported.value = initiallyVariantSupported.value.filter((data) => {
      if (data.lang === deletedvariant.value) {
        saveRemovedCategoryVariants.value.push(deletedvariant.value);
        return false;
      }
      return true;
    });

    recipeVariantList.value.splice(categoryVariantDataIndex.value, 1);

    let langData = {};
    if (deletedvariant.value === "es-US") {
      langData = {
        language: deletedvariant.value,
        language_name: "Spanish",
        language_flag: "/images/flags/spain-flag.png",
      };
    } else if (deletedvariant.value === "fr-FR") {
      langData = {
        language: deletedvariant.value,
        language_name: "French",
        language_flag: "/images/flags/france-flag.png",
      };
    }

    recipeVariantLanguageList.value.push(langData);
    closeModal();
  };

  const inputContentChanged = () => {
    isCampaignModified.value = true;
  };

  const handleDrag = (event) => {
    if (event?.moved || event?.added) isCampaignModified.value = true;
  };

  const updatedRecipeVariantList = (variantList) => {
    if (variantList && variantList.length > 0) {
      variantList.forEach((item) => {
        if (item && item.name !== "" && item.lang !== "") {
          item[item.lang] = { name: item.name ? item.name : "" };
        }
      });
    }

    const updatedVariantList = Object.assign({}, ...variantList);
    delete updatedVariantList.lang;
    delete updatedVariantList.name;

    finalSelectedLanguage.value = Object.keys(updatedVariantList);
    return updatedVariantList;
  };

  const setLanguageVariant = (variantList) => {
    const copyObjectData = [];
    if (finalSelectedLanguage.value.length > 0 && variantList && variantList[lang.value]) {
      finalSelectedLanguage.value.forEach((item) => {
        copyObjectData.push({ [item]: variantList[lang.value] });
      });
    }

    return Object.assign({}, ...copyObjectData);
  };

onUnmounted(() => {
  document.removeEventListener("input", handleClickNameInput);
  document.removeEventListener("click", handleClickOutside);
  document.removeEventListener("click", handleClickOutsidePopup);
});
onEscapeKeyPress(closeModal);

watchEffect(() => {
  $eventBus.emit($keys.KEY_NAMES.CAMPAIGN_MODIFIED, isCampaignModified.value);
  checkVariantNameEmpty();
});

  </script>
