import { useTableManagerStore } from "../stores/table-manager.js";
import { computed, ref } from "vue";
import { KEYS } from "../сonstants/keys.js";
import { useStore } from "vuex";
import { QUERY_PARAM_KEY } from "../сonstants/query-param-key.js";
import { useSearchStore } from "../stores/search.js";
import { useRouter } from "vue-router";
import { isAsyncFunction } from "../utils/is-async-function.js";

export const useTableManager = (
  {
    storeId,
    clientKey,
    endpointKey,
    fetchFn,
    defaultParams = {},
    afterMapParamsFn,
    afterFetchFn,
    defaultPagination = {
      from: 0,
      size: 15,
      total: 0,
    },
    immediate = true,
    smoothUpdate = false,
    smoothUpdateInterval = 10000,
    resultModel,
  }
) => {
  const store = useStore();
  const router = useRouter();
  const route = useRoute();
  const { readyProject } = useProjectLang();
  const { setSearchQuery } = useSearchStore();

  const smoothTimer = ref(null);
  const isSmoothUpdateNeeds = ref(false);
  const isDecrementPageLogicEnabled = ref(true);

  const lang = computed(() => store.getters["userData/getDefaultLang"]);

  const {
    tms_rows,
    tms_pagination,
    tms_isLoading,
    tms_isFirstLoadCompleted,
    tms_fetch,
    tms_setRows,
    tms_setLoading,
    tms_setPaginationFrom,
    tms_setPaginationTotal,
    tms_reset,
  } = useTableManagerStore({
    storeId,
    clientKey,
    endpointKey,
    fetchFn,
    defaultPagination: {
      from: _getParamFrom(),
      size: defaultPagination.size,
      total: defaultPagination.total,
    },
    resultModel,
  });

  const getParams = () => {
    const { search, searchQuery, editPageQuery, editCategoryQuery, editRecipeQuery, editTagQuery } = route.query;
    const searchQueryString = search || searchQuery || editCategoryQuery || editPageQuery || editRecipeQuery || editTagQuery || undefined;

    return {
      q: searchQueryString || undefined,
      from: _getParamFrom(),
      size: defaultPagination.size,
      includeTotals: true,
      lang: lang.value,
      sort: "lastMod",
      ...defaultParams,
    };
  };

  const proxyFetch = async ({ params = {}, isSmoothUpdate = false }) => {
    let paramsObject = Object.keys(params)?.length ? params : getParams();

    if (afterMapParamsFn && typeof afterMapParamsFn === "function") {
      paramsObject = isAsyncFunction(afterMapParamsFn)
        ? await afterMapParamsFn(paramsObject)
        : afterMapParamsFn(paramsObject);
    }

    await tms_fetch({
      params: paramsObject,
      isSmoothUpdate,
    });

    if (afterFetchFn && typeof afterFetchFn === "function") {
      const data = {
        params: paramsObject,
        rows: tms_rows.value,
      };
      isAsyncFunction(afterFetchFn) ? await afterFetchFn(data) : afterFetchFn(data);
    }
  };

  const decrementPageIfNoResults = (rows) => {
    if (!isDecrementPageLogicEnabled.value) return;
    if (!tms_isFirstLoadCompleted.value) return;
    if (rows?.length) return;

    const pageQuery = route?.query[QUERY_PARAM_KEY.PAGE];
    const currentPage  = pageQuery ? Number(pageQuery) : 0;
    if (currentPage > 1) {
      router.push({
        query: {
          ...route.query,
          [QUERY_PARAM_KEY.PAGE]: currentPage > 2 ? pageQuery - 1 : undefined,
        }
      })?.catch();
    }
  };

  const checkIfNeedSmoothUpdateByStates = (data, statesArray) => {
    const states = statesArray || [KEYS.STATE.PUBLISHING, KEYS.STATE.UNPUBLISHING];
    const newState = data?.some((item) => states.includes(item.state)) || false;
    if (isSmoothUpdateNeeds.value !== newState) {
      isSmoothUpdateNeeds.value = newState;
    }
  };

  const runSmoothUpdate = () => {
    if (!smoothUpdate) {
      return;
    }

    const callSmoothUpdate = async () => {
      if (isSmoothUpdateNeeds.value) {
        await proxyFetch({ isSmoothUpdate: true });
      }
    };
    smoothTimer.value = setInterval(() => callSmoothUpdate(), smoothUpdateInterval);
  };

  const reset = ({ emitQueryParam = true } = {}) => {
    isDecrementPageLogicEnabled.value = false;
    tms_reset();
  };

  onMounted(async () => {
    reset({ emitQueryParam: false });
    isDecrementPageLogicEnabled.value = true;

    if (immediate) {
      readyProject(async ({ isProjectReady }) => {
        if (isProjectReady) {
          await proxyFetch({});
          runSmoothUpdate();
        }
      });
    }
  });

  onBeforeUnmount(() => {
    setSearchQuery("", { emitQueryParam: false });

    if (smoothTimer.value) {
      clearInterval(smoothTimer.value);
      smoothTimer.value = null;
    }
  });

  watch(() => route.query, async () => {
    await proxyFetch({});
  });

  watch(tms_rows, (value) => {
    decrementPageIfNoResults(value);
    checkIfNeedSmoothUpdateByStates(value);
  });

  function _getParamFrom () {
    const pageQuery = route?.query[QUERY_PARAM_KEY.PAGE];
    return pageQuery ? (Number(pageQuery) - 1) * defaultPagination.size : defaultPagination.from;
  }

  return {
    tm_rows: tms_rows,
    tm_pagination: tms_pagination,
    tm_isLoading: tms_isLoading,
    tm_isFirstLoadCompleted: tms_isFirstLoadCompleted,
    tm_lang: lang,

    tm_setRows: tms_setRows,
    tm_setLoading:  tms_setLoading,
    tm_setPaginationFrom: tms_setPaginationFrom,
    tm_setPaginationTotal: tms_setPaginationTotal,
    tm_reset: reset,
    tm_fetch: proxyFetch,
    tm_getParams: getParams,
    tm_checkIfNeedSmoothUpdateByStates: checkIfNeedSmoothUpdateByStates,
    tm_runSmoothUpdate: runSmoothUpdate,
  };
}
