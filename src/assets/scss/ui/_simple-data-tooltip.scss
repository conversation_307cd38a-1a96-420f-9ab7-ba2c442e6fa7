
/**
  Class: .simple-data-tooltip-text {}
  Using in the tooltip-if-overflow directive!
 */

.simple-data-tooltip {
  position: relative;

  // simple-data-tooltip-content
  &-content {
    visibility: hidden;
    opacity: 0;
    position: absolute;
    top: 100%;
    transform: translateX(-50%);
    padding: 10px;
    border-radius: 8px;
    max-width: 260px;
    text-align: left;
    z-index: 9999;
    width: 260px;
    box-shadow: 0 4px 10px 0 $shadow-black, 0 3px 5px 0 $faint-black, 0 0 0 1px $shadowy-black;

    > img {
      width: 14px;
      height: 14px;
      margin-top: 1px;
    }
  }

  &:hover &-content {
    visibility: visible;
    opacity: 1;
  }

  &:before,
  &:after {
    display: none;
    z-index: 99999;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
  }

  &:before {
    content: attr(data-tooltip-text);
    bottom: calc(100% + 16px);
    width: max-content;
    max-width: 322px;
    padding: 5px 10px;
    color: $feather-gray;
    font-size: 12px;
    font-weight: 600;
    text-align: center;
    text-transform: initial;
    word-break: break-word;
    white-space: pre-line;
    background-color: $jet-black;
    border-radius: 8px;
    visibility: hidden;
  }

  &:after {
    content: "";
    bottom: calc(100% + 5px);
    border-width: 6px;
    border-style: solid;
    border-color: $jet-black $transparent $transparent $transparent;
    visibility: hidden;
  }

  &:hover:before,
  &:hover:after {
    display: block;
    opacity: 1;
    visibility: visible;
  }

  // simple-data-tooltip-edge
  &-edge {
    &:before {
      left: auto;
      right: 0;
      transform: translateX(0);
    }

    &:after {
      left: auto;
      right: 25px;
      transform: translateX(0);
    }
  }

  // simple-data-tooltip-left
  &-left {
    &:before {
      right: auto;
      left: 0;
      transform: translateX(0);
    }

    &:after {
      right: auto;
      left: 25px;
      transform: translateX(0);
    }
  }

  // simple-data-tooltip-bottom
  &-bottom {
    &:before {
      top: calc(100% + 16px);
      bottom: auto;
    }

    &:after {
      top: calc(100% + 5px);
      bottom: auto;
      border-color: $transparent $transparent $jet-black $transparent;
    }
  }

  // simple-data-tooltip-warn
  &-warn {
    &:before,
    &:hover:before {
      top: calc(100% + -12px);
      border-color: transparent transparent $rose-white transparent;
      visibility: hidden;
    }
    &:after,
    &:hover:after {
      display: none;
    }
  }
  &-warn &-content {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    padding: 12px 14px;
    margin-top: 6px;
    background-color: $rose-white;
    font-size: 13px;
    font-weight: 400;
    color: $graphite-gray;
  }
}
