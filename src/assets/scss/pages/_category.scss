.content-item {
  .content-header {
    display: flex;
    align-items: flex-start;
    gap: 20px;

    .content-details {
      flex: 1;

      .content-details-top {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
      }

      .content-details-body {
        display: flex;
        width: 100%;
        justify-content: space-between;

        .content-details-text {
          margin-bottom: 12px;

          .content-details-text-mark {
            margin-left: 4px;
          }
        }
      }
    }
  }

  .content-hr {
    border: none;
    border-top: 1px solid $grainsboro;
    margin: 24px 0;
  }

  .content-settings {
    .content-settings-container {
      display: flex;
      align-items: center;
      gap: 12px;
      margin-bottom: 16px;

      .content-settings-actions {
        width: 40%;

        .slug-exist-main {
          margin-top: 4px;

          .slug-exist {
            color: $ruby-red;
            font-size: 12px;
          }
        }
      }
    }
  }
}

.recipes-table-content {
  .content {
    .recipe-category {
      .recipe-header-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .recipe-table-content {
        .add-zero-section {
          text-align: center;
          padding: 30px 20px;
          background-color: $white-smoke;
          border-radius: 8px;
          margin: 0 20px 20px 20px;
        }
      }
    }
  }
}

.category-recipe {
  &-image {
    width: 60px;
    height: 60px;
    object-fit: cover;
    border-radius: 4px;
  }

  &-actions {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    gap: 8px;
  }

  &-header {
    &-text {
      width: 50%;
    }

    &-actions {
      display: flex;
      align-items: center;
      width: 100%;
      justify-content: end;
    }
  }

  &-selected-color {
    background: $aqua-spring !important;
  }
}

.content {
  &-recipes {
    &-wrapper,
    &-combined-wrapper {
      padding: 0;
    }

    &-combined-wrapper {
      margin-top: 20px;
    }
  }

  &-variants {
    &-main {
      display: flex;
      justify-content: space-between;
      padding-top: 20px;
      border-top: 1px solid $grainsboro;
    }

    &-card-main {
      width: 25%;
    }
  }
}

.edit {
  &-selection {
    &-container {
      .edit-selection-panel {
        display: flex;
        align-items: center;
        margin: 15px 0px;
        position: relative;
        gap: 34px;

        .edit-btn-container {
          display: flex;
          position: absolute;
          right: 100px;
          align-items: center;
          gap: 100px;
        }

        .edit-selected-text {
          display: flex;
          align-items: center;
          gap: 14px;
          cursor: pointer;
        }
      }
    }
  }

  .edit-select-all-checkbox-section {
    width: 2%;
  }
}

.edit-checkbox-product-section {
  position: relative;
  left: 20px;
}

.add-recipes {
  &-section {
    margin-top: 20px;
  }

  &-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    min-height: 300px;

    .add-recipes-left {
      flex: 1;
      max-width: 450px;

      .add-recipes-header {
        margin-bottom: 30px;
      }
    }

    .add-recipes-right {
      flex: 0 0 auto;
      margin-left: 40px;

      .add-recipes-illustration {
        .pan-image {
          max-width: 291px;
          height: auto;
          object-fit: contain;
        }
      }
    }
  }
}

.content {
  &-recipe {
    &-table {
      td {
        &.simple-table-column {
          &-image {
            width: 10%;
          }

          &-title {
            width: 30%;
          }

          &-actions {
            width: 20%;
          }

          &-isin {
            width: 11%;
          }

          &-totalTime {
            width: 12%;
          }

          &-ingredientCount {
            width: 10%;
          }

          &-checkbox {
            width: 4%;
          }
        }
      }
    }
  }

  &-settings {
    &-actions {
      .name-field-input {
        font: {
          size: 16px;
          weight: 400;
        }
      }
    }
  }
}