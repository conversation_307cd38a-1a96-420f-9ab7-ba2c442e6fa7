<template>
  <nav class="sidebar-section">
    <div
      class="sidebar-logo logo"
      :class="{
        'disabled': isRouteLoading || isLoading
      }"
    >
      <NuxtLink
        :to="!isCampaignModified ? 'overview' : ''"
        event=""
        @click="navigateToOverview"
        no-prefetch
      >
        <img src="@/assets/images/innit.png" class="logo-image" alt="innit logo" />
      </NuxtLink>
    </div>

    <div
      ref="menuListItemsContainerElRef"
      class="sidebar-container"
      :class="{
        'disabled': isRouteLoading || isLoading
      }"
    >
      <ul class="sidebar-menu">
        <template v-for="menu in menuList" :key="menu.uniqueId">
          <li class="sidebar-menu-item">
            <NuxtLink
              v-if="menu.link"
              :to="!isCampaignModified ? menu.link : ''"
              :id="`index${menu.link}`"
              :data-test-id="`index${menu.link}`"
              class="sidebar-menu-item-link font-weight-semi-bold color-white"
              :class="{ '__active': isActive(menu.link, menu.uniqueId) }"
              @click="(event) => handleRoutePageClick(event, menu)"
              :prefetch="false"
            >
            <img
              :src="'/images/icons/' + menu.icon"
              width="20px"
              :alt="menu.name + ' link icon'"
            />
              <span>{{ menu.name }}</span>
            </NuxtLink>
            <button
              v-if="!menu.link"
              type="button"
              class="sidebar-menu-item-link font-weight-semi-bold color-white"
              :class="{ '__active': isActive(menu.link, menu.uniqueId) }"
              @click="routePage(menu, '')"
              :data-test-id="`index/${menu.name}`"
            >
            <img :src="'/images/icons/' + menu.icon" width="20px" :alt="menu.name + ' link icon'" />
              <span>{{ menu.name }}</span>
            </button>
          </li>
          <li v-if="menu.horizontalLine" class="sidebar-menu-item sidebar-menu-item-divider">
            <hr />
          </li>
        </template>
      </ul>
    </div>

    <cancelModal
      v-if="isConfirmModalVisible"
      :availableLang="[]"
      :isCampaignModifiedFromShoppableReview="false"
      :callConfirm="routeChange"
      :closeModal="closeModal"
    />

    <Modal v-if="isOffline">
      <template #editProductMatches>
        <div class="organizations-confirmation-modal">
          <div class="organizations-confirmation-modal-content">
            <div class="confirm-exit-top-section">
              <div class="confirm-exit-image">
                <div class="confirm-exit-image-container">
                  <img alt="confirm exit icon" :src="confirmExitImage" loading="eager" />
                </div>
              </div>
              <div class="confirmation-description">
                Please check your internet connection
              </div>
            </div>
            <div class="organizations-confirmation-button-container">
              <button type="button" class="btn-green" @click="refreshPage()">
                {{ $t('BUTTONS.CONFIRM_BUTTON') }}
              </button>
            </div>
          </div>
        </div>
      </template>
    </Modal>

    <exportRecipe
      v-if="isExportRecipeModalVisible"
      :closeModal="closeExportRecipe"
    ></exportRecipe>

    <importRecipe
      v-if="isImportRecipeModalVisible"
      :closeModal="closeImportRecipe"
    ></importRecipe>
  </nav>
</template>

<script setup>
import { computed, onBeforeUnmount, onMounted, onUpdated, ref } from 'vue';
import cancelModal from "@/components/cancel-modal";
import { useCommonUtils } from "~/composables/useCommonUtils";
import Modal from "@/components/Modal";
import exportRecipe from '../export-recipe.vue';
import importRecipe from '../import-recipe.vue';
import { ROUTES_MAPPING } from "@/сonstants/routeMappings";
import { LOCAL_TRACKER_CONFIG } from "@/сonstants/trackerConfig";
import { useStore } from 'vuex';
import { useRoute, useRouter } from 'vue-router';
import { useNuxtApp } from '#app'
import { useProjectLang } from "../../composables/useProjectLang.js";
import confirmExitImage from '~/assets/images/confirm-exit.png';
import { useSearchStore } from "../../stores/search.js";

const { $eventBus, $tracker, $keys } = useNuxtApp();
const route = useRoute();
const router = useRouter();
const store = useStore();
const { triggerLoading } = useCommonUtils();
const { readyProject } = useProjectLang();
const { isOffline } = useConnectionStatus();

const menuListItemsContainerElRef = ref(null);
const isLoading = ref(true);
const isConfirmModalVisible = ref(false);
const isCampaignModified = ref(false);
const menu = ref([]);
const isRouteLoading = ref(false);
const menus = ref([]);
const isExportRecipeModalVisible = ref(false);
const isImportRecipeModalVisible = ref(false);
const isHelpModalVisible = ref(false);
const selectedMenu = ref("/overview");
const {
  searchQuery,
  setSearchQuery,
} = useSearchStore();

const lang = computed(() => {
  if (store) {
    return store.getters["userData/getDefaultLang"];
  }
  return null;
});

const project = computed(() => {
  if (store) {
    return store.getters["userData/getProject"];
  }
  return null;
});

const menuList = computed(() => {
  if (store) {
    return store.getters["config/getSidebarNavs"];
  }
  return [];
});

const navigateToOverview = () => {
  menu.value = menuList.value?.[0];
  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    router.push({
      path: "/overview",
    });
  }
};
const checkAccessDenied = () => {
  const { path } = route;
  if (path === '/access-denied') {
    routePage('overview');
  }
};

const closeExportRecipe = () => {
  isExportRecipeModalVisible.value = false;
};

const closeImportRecipe = () => {
  isImportRecipeModalVisible.value = false;
};

const refreshPage = () => {
  location.href = `${ window.location.origin }/overview`;
};
const routeChange = () => {
  routePage(menu.value, "changeConfirm");
  closeModal();
};

const handleBeforeUnload = (e) => {
  if (!isCampaignModified.value) return;
  e.preventDefault();
  e.returnValue = "";
};

const handleESCClickOutside = (event) => {
  if (event?.key === "Escape") {
    closeModal();
  }
};
const routePage = (menuItem, field) => {
  if (route.path === menuItem?.link && route.path === "/iq-recipe-generator") {
    return;
  }

  if (field === "changeConfirm") {
    isCampaignModified.value = false;
  }

  $eventBus.on("campaignModified", (data) => {
    isCampaignModified.value = data;
  });

  menu.value = menuItem;

  if (isCampaignModified.value) {
    isConfirmModalVisible.value = true;
  } else {
    const menuEvents = {
      "/recipes": "queryForRecipe",
      "/collections": "collections",
      "/ingredients": "queryForIngredient",
      "/category": "queryForCategory",
      "/cat-group": "queryForCategoryGroup",
      "/tags": "queryForTag",
      "/edit-search": "editSearchMain",
      "/articles": "articleMain",
      "/iq-users": "queryForUser",
      "/iq-recipe-generator": "queryForGenerator",
      "/iq-batch-generator": "queryForBatchGenerator",
    };

    clickEvent(menuItem.uniqueId);

    isHelpModalVisible.value = false;

    const event = menuEvents[menuItem.link];
    if (event) {
      triggerLoading($keys.EVENT_KEY_NAMES.ROUTE_PAGE, event);
    } else if (menuItem.uniqueId === $keys.EVENT_KEY_NAMES.EXPORT) {
      isExportRecipeModalVisible.value = true;
    } else if (menuItem.uniqueId === $keys.EVENT_KEY_NAMES.IMPORTER) {
      isImportRecipeModalVisible.value = true;
    } else if (menuItem.uniqueId === $keys.EVENT_KEY_NAMES.HELP) {
      isHelpModalVisible.value = false;
      window.InnitZammadForm();
    }

    selectedMenu.value = menuItem?.link;
    routePageClicked(menuItem);
  }
};

const handleRoutePageClick = (event, menu) => {
  event.preventDefault();
  routePage(menu, "");
};

const routePageClicked = (menu) => {
  if (searchQuery.value.str?.length && menu?.uniqueId !== $keys?.EVENT_KEY_NAMES.HELP) {
    setSearchQuery("", { emitQueryParam: false });
    nextTick(() => {
      router.push(menu.link);
      newLink(menu.link);
    });
  }

  if (menu.link) {
    if (route.query?.data !== "" && route.path === "/recipes") {
      route.query.data = "";
      router.push(menu.link);
      $eventBus.emit("recipeStatusAll");
    }

    router.push(menu.link);
    newLink(menu.link);
  }

  if (!isCampaignModified.value && menu.name === "Design") {
    menuList.value.forEach((data) => {
      data.displayData = !data.displayData;
    });
  }

  isCampaignModified.value = false;
  triggerLoading($keys?.KEY_NAMES.CAMPAIGN_MODIFIED, isCampaignModified.value);

  if(menu?.uniqueId !== $keys?.EVENT_KEY_NAMES.HELP) {
    setSearchQuery("", { emitQueryParam: false });
  }
};
const clickEvent = (menu) => {
  const eventMap = {
    "overview": $keys.EVENT_KEY_NAMES.CLICK_OVERVIEW,
    "recipes": $keys.EVENT_KEY_NAMES.CLICK_RECIPES,
    "generator": $keys.EVENT_KEY_NAMES.CLICK_GENERATOR,
    "generatorBatch": $keys.EVENT_KEY_NAMES.CLICK_BATCH_GENERATOR,
    "importer": $keys.EVENT_KEY_NAMES.CLICK_IMPORTER,
    "categories": $keys.EVENT_KEY_NAMES.CLICK_CATEGORIES,
    "categoryGroups": $keys.EVENT_KEY_NAMES.CLICK_CATEGORY_GROUP,
    "tags": $keys.EVENT_KEY_NAMES.CLICK_TAGS,
    "ingredients": $keys.EVENT_KEY_NAMES.CLICK_FOOD_ITEMS,
    "featured": $keys.EVENT_KEY_NAMES.CLICK_COLLECTIONS,
    "searchFilters": $keys.EVENT_KEY_NAMES.CLICK_SEARCH_FILTER,
    "organizations": $keys.EVENT_KEY_NAMES.CLICK_ORGANIZATION,
    "export": $keys.EVENT_KEY_NAMES.CLICK_EXPORT,
    "users": $keys.EVENT_KEY_NAMES.CLICK_USERS,
    "settings": $keys.EVENT_KEY_NAMES.CLICK_SETTINGS,
  };

  const eventLabel = eventMap[menu];
  if (eventLabel) {
    $tracker.sendEvent(eventLabel, {}, { ...LOCAL_TRACKER_CONFIG });
  }
};
const closeModal = () => {
  isConfirmModalVisible.value = false;
};

const newLink = (link) => {
  $eventBus.emit("current-link", link);
};

const isActive = (menuLink, menuId) => {
  const { path } = route;

  if (path === '/access-denied') {
    if (typeof selectedMenu.value === "string") {
      return menuLink === selectedMenu.value;
    }
    return menuId === $keys.EVENT_KEY_NAMES.OVERVIEW;
  } else if (isExportRecipeModalVisible.value) {
    return menuId === $keys.EVENT_KEY_NAMES.EXPORT;
  } else if (isImportRecipeModalVisible.value) {
    return menuId === $keys.EVENT_KEY_NAMES.IMPORTER;
  } else if (isHelpModalVisible.value) {
    return menuId === $keys.EVENT_KEY_NAMES.HELP;
  }

  return Object.entries(ROUTES_MAPPING).some(([link, paths]) =>
    menuLink === link && paths.some(pathFragment => path?.includes(pathFragment))
  );
};
const scrollMenuListToTop = () => menuListItemsContainerElRef.value?.scrollTo({ top: 0, behavior: 'smooth' });

onUpdated(() => {
  $eventBus.on("campaignModified", (data) => {
    isCampaignModified.value = data;
  });

  $eventBus.on("routeloading", (data) => {
    isRouteLoading.value = data;
  });
});

onMounted(() => {
  readyProject(() => {
    isLoading.value = false;
  });

  ["projectChanged", "goToRecipe"].forEach((event) => {
    $eventBus.on(event, scrollMenuListToTop);
  });
  checkAccessDenied();

  $eventBus.on("campaignModified", (data) => {
    isCampaignModified.value = data;
  });

  $eventBus.on("routeloading", (data) => {
    isRouteLoading.value = data;
  });

  window.addEventListener("beforeunload", (event) => {
    if (!isCampaignModified.value) {
      return;
    }
    handleBeforeUnload(event);
  });

  document.addEventListener("keyup", handleESCClickOutside);
});

onBeforeUnmount(() => {
  document.removeEventListener("keyup", handleESCClickOutside);
  window.removeEventListener("beforeunload", handleBeforeUnload);

  $eventBus.off("projectChanged");
});
</script>
