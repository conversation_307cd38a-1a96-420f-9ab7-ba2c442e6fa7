<template>
  <div class="recipe-primary-image-main">
    <div
      class="recipe-primary-section"
      :class="{
        'recipe-image-error-section': shouldShowImageErrorSection }"
    >
      <div class="main-image-container">
        <div
          class="main-image-section btn-reset"
          :class="{ 'image-zoom': mainSelectedImage }"
          @click="openImageZoom()"
        >
          <div
            v-if="uploadImagePercentage === 0 || uploadImagePercentage === 100"
            class="main-image-section-image-box"
          >
            <div
              v-if="shouldShowImageTopBottomSection"
              class="image-top-section"
              :class="{
                'image-main-top-section': !selectedImage?.isMainImage,
              }"
            >
              <div v-if="selectedImage?.isMainImage" class="main-image-check" data-test-id="main-check-image">
                <img
                  class="main-check-image"
                  src="@/assets/images/check.svg?skipsvgo=true"
                  alt="check"
                />
                <div class="main-image-text text-light-h3">
                  {{ $t("RECIPE_PRIMARY_IMAGE.MAIN") }}
                </div>
              </div>
              <div v-if="recipeVariantSelectedLanguage === defaultLang" class="delete-button-section">
                <button
                  type="button"
                  class="delete-recipe-button btn-reset"
                  @click.stop="deleteImage()"
                  :class="{ disabled: config?.isRecipeVariant }"
                >
                  <img
                    class="delete-button-image"
                    src="@/assets/images/icons/remove-icon.svg?skipsvgo=true"
                    alt="Delete"
                  />
                </button>
              </div>
            </div>
            <div class="delete-button-container" v-if="isDeleteButtonClicked">
              <button
                type="button"
                class="btn-green-outline white-background"
                @click.stop="closeDeleteSection()"
              >
                {{ $t("BUTTONS.CANCEL_BUTTON") }}
              </button>
              <button
                type="button"
                class="btn-red"
                @click.stop="closeDeleteButton()"
              >
                {{ $t("BUTTONS.DELETE_BUTTON") }}
              </button>
            </div>
            <div
              class="primary-image-text text-title-2 font-normal"
              v-if="
                isRecipeImageGenerated &&
                config.theme === 'recipe-generator-page' &&
                (!mainSelectedImage || !selectedImage)
              "
            >
              {{ $t("RECIPE_PRIMARY_IMAGE.PRIMARY_IMAGE") }}
            </div>
            <div class="main-image-section">
              <div
                v-if="!mainSelectedImage && !mainImage && !computedImageSrc"
                class="main-image"
              >
                <media-empty-box
                  :isRecipePage="config?.theme === 'recipe-details-page'"
                  :isActionNeeded="shouldShowImageErrorSection"
                />
              </div>
              <div v-else class="main-image">
                <img
                  class="main-image-recipe"
                  :src="currentImageSrc"
                  alt="Recipe"
                  @error="$event.target.src = defaultImage"
                />
              </div>
            </div>
            <button
              type="button"
              v-if="imageList?.length && isImageInfoVisible"
              class="image-info-tooltip-section btn-reset"
              @click.stop="showImageInfo()"
              @mouseleave="hideImageInfo()"
            >
              <span class="image-info-text text-h4 font-weight-semi-bold">
                <span class="simple-data-tooltip-content">
                  {{ tooltipText }}
                </span>
              </span>
            </button>
            <button
              type="button"
              v-if="shouldShowImageTopBottomSection"
              class="image-bottom-section btn-reset"
              @click.stop="showImageInfo()"
            >
              <img
                class="info-image"
                src="@/assets/images/icons/info.svg?skipsvgo=true"
                alt="info"
              />
            </button>
          </div>
          <div
            v-if="uploadImagePercentage > 0 && uploadImagePercentage < 100"
            class="progress-loader-section"
          >
            <upload-loader
              :uploadPercentage="uploadImagePercentage"
              :uploadSize="uploadImageSize"
              :loadedSize="loadedImageSize"
            />
          </div>
        </div>
      </div>
      <div class="image-list-wrapper">
        <div class="image-list">
          <div
            v-for="(image, index) in imageList"
            :key="index"
            class="image-container"
            :class="themeClass"
          >
            <ImageSelectButton
              :image="image"
              :isSelected="isImageSelected && selectedImage?.id === image?.id"
              :isMainImage="image?.isMainImage"
              :defaultImage="defaultImage"
              @selectImage="selectImage"
              @hoverImage="setHoveredImage"
              @clearHover="clearHoveredImage"
            />
          </div>
          <div
            v-for="count in emptyBlocksCount"
            :key="'empty-' + count"
            class="empty-block"
            :class="themeClass"
          ></div>
        </div>
      </div>
    </div>
    <div
      v-if="
        config?.showErrorMessage && config?.theme === 'recipe-generator-page'
      "
      class="recipe-select-error-meesage"
    >
      <div class="info-icon">
        <img src="@/assets/images/red-info.svg?skipsvgo=true" alt="info icon" />
      </div>
      <div class="text text-title-2 font-weight-semi-bold">
        {{ $t("RECIPE_PRIMARY_IMAGE.SELECT_MAIN_IMAGE") }}
      </div>
    </div>
    <imageZoomPopup
      :zoomedImage="imageList[selectedImageIndex]?.url"
      :defaultImage="defaultImage"
      :selectedImageIndex="selectedImageIndex"
      @prev-image="updateZoomedImage($keys.KEY_NAMES.PREV)"
      @next-image="updateZoomedImage($keys.KEY_NAMES.NEXT)"
      :imageList="imageList"
      @close-image-zoom="closeImageZoom"
      v-if="isImageZoomed"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import imageZoomPopup from "./image-zoom-popup.vue";
import UploadLoader from "./upload-loader.vue";
import mediaEmptyBox from "./media-empty-box.vue";
import ImageSelectButton from './image-select-button.vue';
import defaultImage from "~/assets/images/default-recipe-image.svg?skipsvgo=true";

const props = defineProps({
  imageList: {
    type: Array,
    default: () => [],
  },
  config: {
    type: Object,
    default: () => ({}),
  },
  uploadImagePercentage: {
    type: Number,
    default: 0,
  },
  loadedImageSize: {
    type: Number,
    default: 0,
  },
  uploadImageSize: {
    type: Number,
    default: 0,
  },
  isRecipeImageGenerated: {
    type: Boolean,
    default: false,
  },
  recipeVariantSelectedLanguage: {
    type: String,
    default: "",
  },
  defaultLang: {
    type: String,
    default: "",
  },
});

const emit = defineEmits();
const selectedImageIndex = ref(0);
const mainSelectedImage = ref(null);
const hoveredImage = ref(null);
const isImageZoomed = ref(false);
const isDeleteButtonClicked = ref(false);
const isImageInfoVisible = ref(false);
const isImageSelected = ref(false);
const mainImage = ref({});
const selectedImage = ref(null);
const styles = ref({
  width: "87px",
  height: "87px",
});


// Computed properties
const tooltipText = computed(() =>props.imageList?.find((image) => image.isMainImage)?.tooltip);

const emptyBlocksCount = computed(() => {
  return Math.max(0, 4 - props.imageList.length);
});

const computedImageSrc = computed(() => {
  if (props.imageList.length && !mainImage.value) {
    setImageData();
  }
  if (props.config.theme === "recipe-generator-page") {
    checkImageData();
    return mainSelectedImage.value;
  } else {
    return mainSelectedImage.value;
  }
});

const currentImageSrc = computed(() => {
  return hoveredImage.value || computedImageSrc.value;
});

const themeClass = computed(() => {
  return {
    [`theme-${props.config.theme}`]: true,
  };
});

const shouldShowImageErrorSection = computed(() => {
  return (props.config.showErrorMessage || props.config.showMainErrorMessage) && !mainImage.value && !props.config.showNoMediaError;
});

const hasImages = computed(() => props.imageList.length > 0);

const isMainImageSelected = computed(() => selectedImage.value?.isMainImage);

const isHoveredOrNotHovered = computed(() => {
  return hoveredImage.value === selectedImage.value?.url || !hoveredImage.value;
});

const shouldShowImageTopBottomSection = computed(() => {
  return hasImages.value && isMainImageSelected.value && isHoveredOrNotHovered.value;
});


const updateZoomedImage = (direction) => {
  const maxIndex = (props.imageList?.length ?? 0) - 1;
  const newIndex = selectedImageIndex.value + (direction === 'next' ? 1 : -1);
  selectedImageIndex.value = Math.max(0, Math.min(newIndex, maxIndex));
};

const setImageData = () => {
  const mainImg = props.imageList?.find((image) => image.isMainImage) || "";
  mainImage.value = mainImg;
  selectedImage.value = mainImg;
  mainSelectedImage.value = mainImg?.url || null;
};

const checkImageData = () => {
  if (!props.imageList?.length) {
    mainImage.value = "";
  }
};

const showImageInfo = () => {
  isImageInfoVisible.value = true;
};

const hideImageInfo = () => {
  isImageInfoVisible.value = false;
};

const selectImage = (img) => {
  selectedImage.value = img;
  isImageSelected.value = true;
  mainSelectedImage.value = props.imageList.find((image) => image?.id === img?.id).url;

  if (selectedImage.value.id === img?.id && img?.isMainImage) {
    mainImageSelection(img.id, false);
    img.isMainImage = false;
    resetImageSelection();
  } else {
    img.isMainImage = true;
    emit('mainImageSelected', img?.id, true);
  }
  closeDeleteSection();
};

const deleteImage = () => {
  if (props.config?.confirmDelete) {
    isDeleteButtonClicked.value = true;
  } else {
    closeDeleteButton();
  }
};

const closeDeleteButton = () => {
  closeDeleteSection();
  emit('deleteImage', selectedImage.value?.id);
  mainImage.value = "";
  resetImageSelection();
};

const closeDeleteSection = () => {
  isDeleteButtonClicked.value = false;
};

const handleClickOutside = (event) => {
  if (
    isDeleteButtonClicked.value &&
    !document.getElementById('image-zoom-container').contains(event.target) &&
    event.target.className !== "delete-recipe-button btn-reset"
  ) {
    closeDeleteSection();
  }
};

const resetImageSelection = () => {
  emit('deleteImage');
  selectedImage.value = null;
  mainImage.value = "";
  mainSelectedImage.value = null;
  isImageSelected.value = false;
};

const openImageZoom = () => {
  if (mainSelectedImage.value) {
    selectedImageIndex.value = props.imageList?.findIndex(
      (image) => image.id === selectedImage.value?.id
    );
    isImageZoomed.value = true;
  }
};

const closeImageZoom = () => {
  isImageZoomed.value = false;
};

const mainImageSelection = (imageId, isMainImage) => {
  if (props.config?.theme === "recipe-generator-page") {
    emit('mainImageSelected', imageId, isMainImage);
  }
};

const setHoveredImage = (image) => {
  hoveredImage.value = image.url;
};

const clearHoveredImage = () => {
  hoveredImage.value = null;
};


// Lifecycle hook
onMounted(() => {
  setImageData();
  document.addEventListener("click", handleClickOutside);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
});


// Watch for imageList updates
watch(() => props.imageList, () => {
  setImageData();
}, { immediate: true });
</script>
