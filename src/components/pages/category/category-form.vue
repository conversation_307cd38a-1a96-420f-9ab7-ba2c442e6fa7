<template>
  <content-wrapper
    wrapper-classes="content-item font-family-averta"
    :is-body-loading="props.isEdit ? isLoading : false"
  >
    <page-top-block
      :hint="hintID"
      :page-actions-back-label="$t('CATEGORY.BACK_MESSAGE')"
      :page-actions-back-path="'/category'"
      :background-image="image"
      :page-actions-is-continue-disabled="!isContinueButtonEnabled"
      :page-actions-continue-label="continueButtonLabel"
      @page-actions-continue="openPreviewSaveModal"
      @page-actions-cancel="() => navigateTo('/category')"
    >
      <div class="content-header">
        <image-box
          :image-src="image"
          :loaded-image-size="loadedImageSize"
          :uploadImagePercentage="uploadImagePercentage"
          @uploadedFile="uploadFile"
        />
        <div class="content-details">
          <div class="content-details-top border-bottom-dashed">
            <name-field
              v-model:name="categoriesName"
              input-id="category-name"
              :input-placeholder="'Name your category'"
            />

            <div
              class="publish-btn-wrapper"
              :class="{
                'simple-data-tooltip': categoriesState !== 'published',
              }"
              :data-tooltip-text="
                categoriesState !== 'published' &&
                'Category must be published to enable toggle'
              "
            >
              <button-with-switcher
                class="content-details-publish-btn"
                :label="$t('COMMON.PUBLISH')"
                :is-checked="isCategoriesStatus === 'active'"
                :is-disabled="categoriesState !== 'published'"
                @action="
                  categoriesName.length > 0
                    ? publishToggleBtnAsync()
                    : publishToggleBtnPopup()
                "
              />
            </div>
          </div>

          <div class="content-settings">
            <div class="content-settings-container">
              <div class="content-settings-name">
                <span class="text-title-3">{{
                  $t("RECIPE_PREVIEW.SLUG")
                }}</span>
              </div>
              <div class="content-settings-actions border-bottom-dashed">
                <name-field
                  v-model:name="categoriesSlug"
                  input-id="slug-category"
                  @input="handleSlugInput"
                  :isRequired="false"
                />
                <div v-if="hasSlugExist" class="slug-exist-main">
                  <p class="slug-exist text-light-h4">
                    {{ $t("COMMON.SLUG_ALREADY_EXISTS") }}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div class="content-details-body">
            <div class="content-details-text font-size-base">
              <span class="font-bold color-grey"
                >{{ $t("CATEGORY.CATEGORY_IMAGE") }}:</span
              >
              <span class="font-normal color-grey">{{
                $t("CATEGORY.IMAGE_FORMAT")
              }}</span>
              <span class="content-details-text-mark color-red">*</span>
            </div>
            <div
              v-if="props.isEdit"
              :class="{
                'simple-data-tooltip simple-data-tooltip-warn':
                  recipeDataForCategories.length ||
                  categoryPromotedRecipes.length,
              }"
            >
              <div
                class="simple-data-tooltip-content"
                v-if="
                  recipeDataForCategories.length ||
                  categoryPromotedRecipes.length
                "
              >
                <img
                  src="@/assets/images/info.svg?skipsvgo=true"
                  alt="info-icon"
                  class="tooltip-icon"
                />
                <span>{{ $t("CATEGORY.CATEGORIES_DELETE_INFO") }}</span>
              </div>
              <button
                type="button"
                class="btn-red-text btn-small"
                @click="deleteCategory()"
                :class="{
                  'simple-data-tooltip': isCategoryIncludeInHero,
                }"
                :disabled="
                  isCategoryIncludeInHero ||
                  recipeDataForCategories.length > 0 ||
                  categoryPromotedRecipes.length > 0
                "
                :data-tooltip-text="
                  isCategoryIncludeInHero && $t('ARTICLE_IN_HERO')
                "
              >
                <img alt="delete-icon" src="@/assets/images/delete-icon.png" />
                <span>{{ $t("CATEGORY.DELETE_CATEGORY") }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
      <div
        v-if="finalAvailableLangs && finalAvailableLangs.length > 1"
        class="content-variant-section"
      >
        <div class="content-variants-main">
          <div class="content-variants text-h5 font-normal">
            {{ $t("CATEGORY.CATEGORY_VARIANTS") }}:
            <div
              v-show="isCategoryAlertIcon"
              class="tag-variant-tooltip-section"
            >
              <div
                class="tooltip-main-container-for-tag-variant simple-data-tooltip"
                :data-tooltip-text="langVariantTooltip"
              >
                <img
                  alt="alert"
                  class="alert-image"
                  src="@/assets/images/red-info.svg?skipsvgo=true"
                />
              </div>
            </div>
          </div>
          <div
            class="add-variant-section"
            :class="{
              'simple-data-tooltip': recipeVariantLanguageList.length < 1,
            }"
            :data-tooltip-text="
              recipeVariantLanguageList.length < 1 &&
              $t('COMMON.ADD_ONLY_ONE_VARIANT')
            "
          >
            <button
              type="button"
              class="btn-green-text btn-small"
              :disabled="recipeVariantLanguageList.length < 1"
              @click="openRecipeVariantPopUp()"
            >
              <img alt="download" src="@/assets/images/category-add.png" />
              <span>{{ $t("BUTTONS.ADD_VARIANT") }}</span>
            </button>
          </div>
        </div>
        <div
          class="add-content-variant color-gray"
          v-if="recipeVariantList.length <= 0"
        >
          {{ $t("CATEGORY.CATEGORY_VARIANTS_INFO") }}
        </div>
        <div class="content-variants-card-main" v-else>
          <template
            v-for="(categoryVariant, index) in recipeVariantList"
            :key="categoryVariant.lang"
          >
            <variant-card-field
              v-if="categoryVariant?.lang !== lang"
              v-model="categoryVariant.name"
              :prefix-label="displayLanguageCode(categoryVariant.lang)"
              input-placeholder="Enter name"
              :is-delete-action-disabled="
                isDeleteVariantVisible(categoryVariant)
              "
              delete-action-tooltip-text="Cannot delete because category variant is used by recipes or category groups."
              @input-change="inputContentChanged()"
              @delete-action="deleteCategoryVariant(categoryVariant, index)"
            ></variant-card-field>
          </template>
        </div>
      </div>
    </page-top-block>
    <simple-content-wrapper
      class="add-recipes-section"
      v-if="
        !props.isEdit &&
        recipeDataForCategories.length === 0 &&
        categoryPromotedRecipes.length === 0
      "
    >
      <div class="add-recipes-content">
        <div class="add-recipes-left">
          <div class="add-recipes-header">
            <h2 class="text-h2 font-normal">
              {{ $t("CATEGORY.ADD_RECIPE_TO_CATEGORY") }}
            </h2>
            <p class="text-title-2 font-normal color-grey">
              {{ $t("CATEGORY.ADD_RECIPES_TO_NEW_CATEGORY") }}
            </p>
          </div>
          <button type="button" class="btn-green" @click="addRecipeToCategory">
            {{ $t("COMMON.ADD_RECIPES") }}
          </button>
        </div>
        <div class="add-recipes-right">
          <div class="add-recipes-illustration">
            <img
              alt="Pan"
              class="pan-image"
              src="@/assets/images/pan-image.png"
            />
          </div>
        </div>
      </div>
    </simple-content-wrapper>

    <simple-content-wrapper
      class="content-recipes-combined-wrapper"
      v-if="
        props.isEdit ||
        (!props.isEdit &&
          (recipeDataForCategories.length > 0 ||
            categoryPromotedRecipes.length > 0))
      "
    >
      <div class="recipes-table-content">
        <div class="content">
          <div class="recipe-category">
            <div class="recipe-header-section">
              <div class="category-recipe-header-text">
                <span class="font-size-24 text-h2 font-normal">
                  {{ categoryPromotedRecipesTotal }}
                  {{
                    categoryPromotedRecipesTotal === 1
                      ? $t("CATEGORY.PROMOTED_RECIPE")
                      : $t("CATEGORY.PROMOTED_RECIPES")
                  }}
                </span>
                <div class="text-title-2 font-normal">
                  <span class="color-grey">{{
                    $t("CATEGORY.SHOW_CATEGORY_PROMOTED_TEXT")
                  }}</span>
                </div>
              </div>
            </div>

            <div class="recipe-table-content">
              <div
                class="add-zero-section"
                v-if="
                  (!categoryPromotedRecipes ||
                    categoryPromotedRecipes.length === 0) &&
                  !isPageLoading &&
                  !isUpdating
                "
              >
                <div class="zero-promoted">
                  <span class="text-title-2 color-spanish-gray">
                    0 {{ $t("CATEGORY.PROMOTED_RECIPES") }}.
                  </span>
                  <span class="text-title-2 font-normal color-spanish-gray">
                    {{ $t("CATEGORY.RECIPE_AUTO_SELECTED") }}
                  </span>
                </div>
              </div>

              <simple-table
                v-if="categoryPromotedRecipes.length > 0"
                :column-names="promotedRecipeColumnNames"
                :column-keys="promotedRecipeColumnKeys"
                :data-source="categoryPromotedRecipes"
                table-class="content-recipe-table"
              >
                <template v-slot:image="props">
                  <img
                    :src="getRecipeImage(props.data)"
                    :alt="props.data?.title"
                    class="category-recipe-image"
                  />
                </template>

                <template v-slot:isin="props">
                  <span class="text-light-h4 color-stone-gray">{{
                    props.data?.isin
                  }}</span>
                </template>

                <template v-slot:title="props">
                  <span class="text-h3 font-bold">{{
                    getRecipeTitle(props.data)
                  }}</span>
                  <languages-alert
                    :languages="props.data?.langs"
                    :has-alert="props.data?.hasAlert"
                    :alert-tooltip-text="$t('COMMON.RECIPE_LANG_ALERT')"
                  ></languages-alert>
                </template>

                <template v-slot:totalTime="props">
                  <span class="text-light-h3">{{
                    getRecipeTime(props.data)
                  }}</span>
                </template>

                <template v-slot:ingredientCount="props">
                  <span class="text-light-h3"
                    >{{ getIngredientCount(props.data) }} ingredients</span
                  >
                </template>

                <template v-slot:actions="props">
                  <div class="category-recipe-actions">
                    <body-menu
                      :actions="getPromotedRecipeActions(props.data)"
                      @call-actions="handleRecipeAction"
                    />
                  </div>
                </template>
              </simple-table>
            </div>
          </div>
        </div>
      </div>

      <div class="recipes-table-content">
        <div class="content">
          <div class="recipe-category">
            <div class="recipe-header-section">
              <div class="category-recipe-header-text">
                <span class="font-size-24 text-h2 font-normal">
                  {{ recipeForCategoriesTotal }}
                  {{
                    recipeForCategoriesTotal === 1
                      ? $t("CATEGORY.RECIPE_IN_CATEGORY")
                      : $t("CATEGORY.RECIPES_IN_CATEGORY")
                  }}
                </span>
              </div>
              <div
                class="category-recipe-header-actions"
                v-if="!isSelectionEnabled"
              >
                <button
                  type="button"
                  class="btn-green-text btn-small"
                  @click="selectRecipes"
                  v-if="recipeDataForCategories.length > 0"
                >
                  {{ $t("COMMON.SELECT") }}
                </button>
                <search-bar
                  :isInForm="true"
                  customPlaceholder="Search for recipe name"
                />
                <button
                  type="button"
                  class="btn-green-text btn-small"
                  @click="addRecipeToCategory()"
                >
                  <img
                    alt="add"
                    src="@/assets/images/category-add.png?skipsvgo=true"
                  />
                  <span>{{ $t("BUTTONS.ADD_RECIPE") }}</span>
                </button>
              </div>
            </div>

            <simple-sticky-wrapper
              v-if="isSelectionEnabled"
              :top="70"
              :distance="-60"
              class="edit-selection-container"
            >
              <div class="edit-selection-panel">
                <div class="edit-select-all-checkbox-section">
                  <label class="edit-checkbox-section checkbox">
                    <input
                      type="checkbox"
                      :checked="selectionOfRecipes[0].isSelected"
                      @click="selectAllMatches()"
                    />
                    <span class="checkmark"></span>
                  </label>
                </div>
                <button
                  type="button"
                  @click="selectAllMatches()"
                  class="btn-reset text-h3"
                >
                  {{ $t("PAGE.RECIPES.SELECT_ALL") }}
                </button>
                <div class="edit-selection">
                  <div class="edit-selected-text">
                    {{ checkSelectedRecipes }} {{ $t("COMMON.SELECTED") }}
                    <span
                      v-if="checkSelectedRecipes > 0"
                      class="edit-selected-cross-icon"
                    >
                      <img
                        src="@/assets/images/close.svg?skipsvgo=true"
                        @click="removeAllSelected()"
                        alt="edit-close-icon"
                      />
                    </span>
                  </div>
                </div>
                <div class="edit-btn-container">
                  <button
                    type="button"
                    class="btn-red"
                    :disabled="checkSelectedRecipes == 0"
                    @click="deleteSelect()"
                  >
                    {{ $t("BUTTONS.REMOVE_BUTTON") }}
                  </button>
                  <button
                    type="button"
                    class="btn-green-text btn-small"
                    @click="cancelSelect()"
                  >
                    {{ $t("BUTTONS.CANCEL_BUTTON") }}
                  </button>
                </div>
              </div>
            </simple-sticky-wrapper>

            <div class="recipe-table-content">
              <div
                class="add-zero-section content-recipe-section"
                v-if="recipeDataForCategories.length === 0 && !isPageLoading"
              >
                <div class="zero-promoted">
                  <span class="text-title-2 color-spanish-gray"
                    >0 {{ $t("CATEGORY.RECIPES_IN_CATEGORY") }}.</span
                  >
                  <span class="text-title-2 font-normal color-spanish-gray">{{
                    $t("CATEGORY.ADD_RECIPE_TO_CATEGORY")
                  }}</span>
                </div>
              </div>

              <simple-table
                v-if="recipeDataForCategories.length > 0"
                :column-names="categoryRecipeColumnNames"
                :column-keys="categoryRecipeColumnKeys"
                :data-source="recipeDataForCategories"
                table-class="content-recipe-table"
                :row-class="
                  (data, index) =>
                    isSelectionEnabled && data.isSelectedToDelete
                      ? 'recipe-selected-color'
                      : ''
                "
                @row-click="tableRowClickAction"
                :body-style="{
                  cursor: isSelectionEnabled ? 'pointer' : 'default',
                }"
              >
                <template v-slot:checkbox="props" v-if="isSelectionEnabled">
                  <div class="edit-product-table-checkbox">
                    <div class="edit-select-all-checkbox-section">
                      <label class="edit-checkbox-section checkbox" @click="(event) => handleCheckboxClick(props.index, props.data, event)">
                        <input
                          @click="(event) => handleCheckboxClick(props.index, props.data, event)"
                          :checked="props.data.isSelectedToDelete || false"
                          type="checkbox"
                        />
                        <span class="checkmark" @click="(event) => handleCheckboxClick(props.index, props.data, event)"></span>
                      </label>
                    </div>
                  </div>
                </template>

                <template v-slot:image="props">
                  <img
                    :src="getRecipeImage(props.data)"
                    :alt="props.data?.title"
                    class="category-recipe-image"
                  />
                </template>

                <template v-slot:isin="props">
                  <span class="text-light-h4 color-stone-gray">{{
                    props.data?.isin
                  }}</span>
                </template>

                <template v-slot:title="props">
                  <span class="text-h3 font-bold">{{
                    getRecipeTitle(props.data)
                  }}</span>
                  <languages-alert
                    :languages="props.data?.langs"
                    :has-alert="props.data?.hasAlert"
                    :alert-tooltip-text="$t('COMMON.RECIPE_LANG_ALERT')"
                  ></languages-alert>
                </template>

                <template v-slot:totalTime="props">
                  <span class="text-light-h3">{{
                    getRecipeTime(props.data)
                  }}</span>
                </template>

                <template v-slot:ingredientCount="props">
                  <span class="text-light-h3"
                    >{{ getIngredientCount(props.data) }} ingredients</span
                  >
                </template>

                <template v-slot:actions="props">
                  <div
                    class="category-recipe-actions"
                    v-if="!isSelectionEnabled"
                  >
                    <div
                      :class="{
                        'simple-data-tooltip': props.data.status !== 'active',
                      }"
                      :data-tooltip-text="props.data.status !== 'active' && $t('COMMON.CANNOT_PROMOTE_UNPUBLISHED_RECIPES')"
                    >

                      <button
                        type="button"
                        class="btn-green-outline"
                        :disabled="props.data.status !== 'active'"
                        @click="promoteRecipe(props.data)"
                      >
                        {{ $t('COMMON.PROMOTE') }}
                      </button>
                    </div>
                    <body-menu
                      :actions="getCategoryRecipeActions(props.data)"
                      @call-actions="handleRecipeAction"
                    />
                  </div>
                </template>
              </simple-table>

              <pagination
                v-if="
                  recipeForCategoriesTotal > sizeRecipe &&
                  recipeDataForCategories.length > 0
                "
                :list="recipeDataForCategories"
                :list-total="recipeForCategoriesTotal"
                :size-per-page="sizeRecipe"
                :current-page="Math.floor(fromRecipe / sizeRecipe) + 1"
                @page-change="handleRecipesPageChange"
              />
            </div>
          </div>
        </div>
      </div>
    </simple-content-wrapper>

    <select-the-language-modal
      v-if="hasRecipeVariantLanguagePopUp"
      :closeModal="() => (hasRecipeVariantLanguagePopUp = false)"
      @preventEnterAndSpaceKeyPress="(event) => event.preventDefault()"
      @nextVariantPopUp="nextCategoryVariantNameModalPopUp"
      @setRecipeVariantLanguageMatches="setRecipeVariantLanguageMatches"
      @showRecipeVariantLanguageMatches="showRecipeVariantLanguageMatches"
      :recipeVariantLanguageList="recipeVariantLanguageList"
      :hasRecipeVariantLanguageResult="hasRecipeVariantLanguageResult"
    />

    <add-variant
      v-if="isAddVariantCategoryNamePopUp"
      :closeModal="() => (isAddVariantCategoryNamePopUp = false)"
      :typeName="'Category'"
      :addVariantSelectedLanguage="recipeVariantSelectedLanguage"
      :itemName="categoriesName"
      @addConfirmVariant="addRecipeVariant"
      @preventEnterAndSpaceKeyPress="(event) => event.preventDefault()"
      @backToRoute="backToSelectLanguageVariantPopUp"
    />
  </content-wrapper>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from "vue";
import { useRoute, useRouter, onBeforeRouteLeave } from "vue-router";
import { useNuxtApp } from "#app";
import { useCategoryStore } from "@/stores/category.js";
import { useUserDataStore } from "@/stores/userData.js";
import { usePreSignedUrlStore } from "@/stores/preSignedUrl.js";
import { useIsinStore } from "@/stores/isin.js";
import { useEditSearchStore } from "@/stores/editSearch.js";
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import PageTopBlock from "@/components/page-top-block.vue";
import SimpleContentWrapper from "@/components/simple-content-wrapper.vue";
import SimpleTable from "@/components/simple-table/simple-table.vue";
import SimpleStickyWrapper from "@/components/simple-sticky-wrapper.vue";
import SearchBar from "@/components/search-bar/search-bar.vue";
import Pagination from "@/components/pagination.vue";
import ImageBox from "@/components/image-box.vue";
import NameField from "@/components/name-field.vue";
import ButtonWithSwitcher from "@/components/button-with-switcher.vue";
import SaveModal from "@/components/save-modal.vue";
import CancelModal from "@/components/cancel-modal.vue";
import SavingModal from "@/components/saving-modal.vue";
import DeleteModal from "@/components/delete-modal.vue";
import AddRecipeModal from "@/components/add-recipe-modal.vue";
import BodyMenu from "@/components/body-menu.vue";
import RecipePreviewDetailModal from "@/components/pages/recipes/recipe-preview-detail-modal.vue";
import VariantCardField from "@/components/variant-card-field/variant-card-field.vue";
import SelectTheLanguageModal from "@/components/select-the-language.vue";
import AddVariant from "@/components/add-variant.vue";
import { PROCESS_MODAL_TYPE } from '@/models/process-modal.model';
import ProcessModal from '@/components/modals/process-modal.vue';
import { useBaseModal } from "@/composables/useBaseModal.js";
import { useProjectLang } from "@/composables/useProjectLang";
import { useTimeUtils } from "@/composables/useTimeUtils";
import { useQueryUtils } from "@/composables/useQueryUtils";
import defaultImage from "@/assets/images/default_recipe_image.png";
import savePopupImage from "@/assets/images/1014367-MQuADjfW4ulIQ-en-US-0.png";
import publishVariantIcon from "@/assets/images/publish-variant-icon.png";
import frenchFlag from "@/assets/images/france-flag.png";
import spanishFlag from "@/assets/images/spain-flag.png";
import LanguagesAlert from "@/components/languages-alert.vue";
import ConfirmModal from "../../modals/confirm-modal.vue";
import { CONFIRM_MODAL_TYPE } from "../../../models/confirm-modal.model.js";
import axios from "axios";

const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false,
  },
});

const route = useRoute();
const router = useRouter();
const categoryStore = useCategoryStore();
const userDataStore = useUserDataStore();
const preSignedUrlStore = usePreSignedUrlStore();
const isinStore = useIsinStore();
const editSearchStore = useEditSearchStore();
const { $t, $keys } = useNuxtApp();
const { readyProject } = useProjectLang();
const { parseDurationString } = useTimeUtils();
const { getSearchQuery } = useQueryUtils();
const { scrollToTop, triggerLoading } = useCommonUtils();

const { openModal, closeModal } = useBaseModal({
  CategorySaveModal: SaveModal,
  CategoryCancelModal: CancelModal,
  CategorySavingModal: SavingModal,
  CategoryDeleteModal: DeleteModal,
  CategorySelectDeleteModal: DeleteModal,
  CategoryAddRecipeModal: AddRecipeModal,
  CategoryRecipePreviewModal: {
    component: RecipePreviewDetailModal,
    hideCloseBtn: false,
    modalWrapperClass: "recipe-preview-detail-modal-wrapper",
    skipClickOutside: true,
  },
  deletingModal: {
    component: ProcessModal,
    skipClickOutside: true,
    skipEscapeClick: true,
    props: {
      modalType: PROCESS_MODAL_TYPE.DELETING,
    },
  },
  confirmModal: ConfirmModal
});

const isLoading = ref(true);
const categoriesName = ref("");
const categoriesSlug = ref("");
const image = ref("");
const isPublish = ref(false);
const uploadImagePercentage = ref(0);
const loadedImageSize = ref(0);
const hasSlugExist = ref(false);
const hasChanges = ref(false);
const isInitializing = ref(true);
const categoryISIN = ref("");
const isAbortedCheckingOperationStatus = ref(false);
const recipeVariantSelectedLanguage = ref("");
const removeRecipeData = ref({});
const isCategoriesStatus = ref("hidden");
const categoriesState = ref("published");
const isCategoryIncludeInHero = ref(false);

const recipeDataForCategories = ref([]);
const categoryPromotedRecipes = ref([]);
const searchConfig = ref({});
const recipeForCategoriesTotal = ref(0);
const categoryPromotedRecipesTotal = ref(0);
const fromRecipe = ref(0);
const sizeRecipe = ref(10);
const isPageLoading = ref(false);
const isUpdating = ref(false);

const searchcopy = ref("");
const totalPromotedRemovedIsin = ref([]);
const addedIsins = ref([]);
const lang = computed(() => userDataStore.getDefaultLang);

const selectedCategoryRecipe = ref([]);
const recipesAfterPageChange = ref([]);
const recipeMatchesIsinsTagRemove = ref([]);
const recipesWereAddedInModal = ref(false);

const isSelectionEnabled = ref(false);
const selectedProducts = ref([]);
const selectedProductsAcrossPages = ref([]);
const selectionOfRecipes = ref([
  {
    isSelected: false,
  },
]);
const categoryVariantDataIndex = ref("");
const finalSelectedLanguage = ref([]);
const recipeMatchesIsinsRemove = ref([]);
const removeRecipeList = ref([]);
const filteredRecipeIsins = ref([]);

const recipeVariantList = ref([]);
const initiallyVariantSupported = ref([]);
const saveRemovedCategoryVariants = ref([]);
const recipeVariantLanguageList = ref([]);
const recipeVariantLanguage = ref("");
const recipeVariantLanguageIndex = ref(0);
const variantName = ref("");
const hasRecipeVariantLanguagePopUp = ref(false);
const hasRecipeVariantLanguageResult = ref(false);
const hasDisableSelectLanguageButton = ref(false);
const isAddVariantCategoryNamePopUp = ref(false);
const categoryAssociations = ref({});
const finalAvailableLangs = ref([]);
const isCategoryAlertIcon = ref(false);
const langVariantTooltip = ref("");
const selectedDefaultLang = ref([]);
const newIsin = ref("");
const imageResponseUrl = ref("");

const RECIPE_ACTION_CASE = {
  PREVIEW: "preview",
  REMOVE: "remove",
  PROMOTE: "promote",
  UNPROMOTE: "unpromote",
};

const promotedRecipeColumnNames = [
  "",
  "Recipe ISIN",
  "Recipe Title",
  "Total Time",
  "Ingredient Count",
  "",
];
const promotedRecipeColumnKeys = [
  "image",
  "isin",
  "title",
  "totalTime",
  "ingredientCount",
  "actions",
];

const categoryRecipeColumnNames = computed(() => {
  if (isSelectionEnabled.value) {
    return [
      "",
      "",
      "Recipe ISIN",
      "Recipe Title",
      "Total Time",
      "Ingredient Count",
      "",
    ];
  }
  return [
    "",
    "Recipe ISIN",
    "Recipe Title",
    "Total Time",
    "Ingredient Count",
    "",
  ];
});

const categoryRecipeColumnKeys = computed(() => {
  if (isSelectionEnabled.value) {
    return [
      "checkbox",
      "image",
      "isin",
      "title",
      "totalTime",
      "ingredientCount",
      "actions",
    ];
  }
  return ["image", "isin", "title", "totalTime", "ingredientCount", "actions"];
});

const hintID = computed(() => {
  return props.isEdit && categoryISIN.value
    ? `ISIN: ${categoryISIN.value}`
    : "";
});

const isCategoryValid = computed(() => {
  return (
    categoriesName.value.trim() !== "" &&
    image.value !== "" &&
    image.value !== defaultImage
  );
});

const continueButtonLabel = computed(() => {
  return isCategoriesStatus.value === "active" ? "Publish" : "Save";
});

const isContinueButtonEnabled = computed(() => {
  const hasNameOrEmpty = recipeVariantList.value.length === 0 || !!recipeVariantList.value[0]?.name;
  return (props.isEdit ? hasChanges.value : isCategoryValid.value) && hasNameOrEmpty;
});

const selectedRecipeCount = ref(0);

const checkSelectedRecipes = selectedRecipeCount;

const updateSelectionCount = () => {
  const currentPageSelectedIsins = recipeDataForCategories.value
    .filter((data) => data.isSelectedToDelete === true)
    .map(item => item.isin);
  const otherPagesCount = selectedProductsAcrossPages.value.filter(
    item => !currentPageSelectedIsins.includes(item.isin)
  ).length;
  const totalCount = currentPageSelectedIsins.length + otherPagesCount;
  selectedRecipeCount.value = totalCount;
  return totalCount;
};

const uploadFile = (file) => {
  if (!file) {
    return;
  }

  const size = parseInt(file.size.toFixed(0));
  if (size > 1 * 1024 * 1024 && size < 15 * 1024 * 1024) {
    openModal({
      name: "confirmModal",
      props: {
        modalType: CONFIRM_MODAL_TYPE.CONTINUE_LOAD,
        title: $t('DESCRIPTION_POPUP.LARGER_FILE'),
        descriptionRed: $t('DESCRIPTION_POPUP.OPTIMAL_IMAGE'),
      },
      onClose: (response) => response && uploadImageAsync(file),
    });
    return;
  } else if (size >= 15 * 1024 * 1024) {
    openModal({
      name: "confirmModal",
      props: {
        modalType: CONFIRM_MODAL_TYPE.UNABLE,
        title: $t('DESCRIPTION_POPUP.MAX_IMAGE_SIZE'),
        description: $t('DESCRIPTION_POPUP.MAX_IMAGE'),
        hideCancelBtn: true
      },
    });
    return;
  }

  uploadImageAsync(file);
};

const uploadImageAsync = async (file) => {
  const reader = new FileReader();
  reader.addEventListener("load", async () => {
    if (!reader.result) {
      return;
    }
    uploadImagePercentage.value = 1;
    loadedImageSize.value = 0;
    const params = {
      entity: "category",
      content: "image",
      extension: file?.type.split("/")[1],
      lang: lang.value,
      public: true,
    };
    const response = await preSignedUrlStore.getPreSignedImageUrlAsync(newIsin.value, params);
    const imageUrl = response?.data?.url || "";
    imageResponseUrl.value = imageUrl;
    await uploadImageFileAsync(imageUrl, file);
    image.value = reader.result;
    if (props.isEdit && !isInitializing.value) {
      hasChanges.value = true;
    }
  }, false);
  reader.readAsDataURL(file);
};

const uploadImageFileAsync = async (url, file) => {
  try {
    const uploadedImageFunction = (data) => {
      if (data === 100) {
        uploadImagePercentage.value = 99;
        setTimeout(() => uploadImagePercentage.value = 100, 2000);
      }
    };
    const cancelToken = axios?.CancelToken ? axios.CancelToken.source() : {};
    await axios.put(url, file, {
      headers: {
        "Content-Type": file.type,
        "x-amz-acl": "public-read",
      },
      cancelToken: cancelToken?.token,
      onUploadProgress: function (progressEvent) {
        uploadImagePercentage.value = parseInt(Math.round((progressEvent.loaded / progressEvent.total) * 100));
        uploadedImageFunction(uploadImagePercentage.value);
        loadedImageSize.value = progressEvent.loaded;
      }.bind(this),
    });
  } catch (e) {
    console.error("[IQ][CategoryForm] Cannot upload image.", e);
  }
};

watch(categoriesName, (newValue, oldValue) => {
  if (props.isEdit && !isInitializing.value && newValue !== oldValue) {
    hasChanges.value = true;
  }
});
watch(isCategoriesStatus, (newValue, oldValue) => {
  if (props.isEdit && !isInitializing.value && newValue !== oldValue) {
    hasChanges.value = true;
  }
});
watch(categoriesSlug, (newValue, oldValue) => {
  if (props.isEdit && !isInitializing.value && newValue !== oldValue) {
    hasChanges.value = true;
  }
});

const handleSlugInput = () => {
  if (props.isEdit && !isInitializing.value) {
    hasChanges.value = true;
  }
};

const publishToggleBtnAsync = async () => {
  if (categoriesState.value !== "published") {
    return;
  }

  if (isCategoriesStatus.value === "active") {
    openModal({
      name: "confirmModal",
      component: ConfirmModal,
      props: {
        modalType: CONFIRM_MODAL_TYPE.UNPUBLISH,
        title: "Do you want to unpublish this category?",
        descriptionRed: "Unpublishing this category will remove it from all associated category group(s) and the widget. Proceed?",
        hideCancelBtn: false
      },
      onClose: (response) => {
        if (response) {
          isCategoriesStatus.value = "hidden";
        }
      },
    });
    if (props.isEdit && !isInitializing.value) {
      hasChanges.value = true;
    }
  } else {
    openModal({
      name: "confirmModal",
      component: ConfirmModal,
      props: {
        modalType: CONFIRM_MODAL_TYPE.EDIT,
        title: "Are you sure you want to publish?",
        hideCancelBtn: false,
        confirmBtnLabel: $t("BUTTONS.CONFIRM_BUTTON")
      },
      onClose: (response) => {
        if (response) {
          isCategoriesStatus.value = "active";
        }
      },
    });
    if (props.isEdit && !isInitializing.value) {
      hasChanges.value = true;
    }
  }
};

const publishToggleBtnPopup = () => {
  if (categoriesState.value === "published") {
    openModal({
      name: "confirmModal",
      component: ConfirmModal,
      props: {
        modalType: CONFIRM_MODAL_TYPE.UNABLE,
        title: "Unable to Publish",
        description: "Please fill in all Required data",
        hideCancelBtn: true
      }
    });
  } else {
    publishToggleBtnAsync();
  }
};

const deleteCategory = () => {
  if (!props.isEdit || !categoryISIN.value) return;

  openModal({
    name: "CategoryDeleteModal",
    props: {
      closeModal: () => closeModal("CategoryDeleteModal"),
      productInfoTitle: "Delete Category?",
      productDescriptionOne: "Do you want to delete this",
      productDescriptionTwo: "category?",
      deleteItem: deleteCategoryConfirm,
      availableLanguage: 0,
      buttonText: "Delete",
    },
  });
};

const deleteCategoryConfirm = async () => {
  try {
    closeModal("CategoryDeleteModal");

    openModal({ name: 'deletingModal' });

    await categoryStore.deleteCategoryList(categoryISIN.value);

    closeModal('deletingModal');
    hasChanges.value = false;

    setTimeout(() => {
      router.push("/category");
    }, 100);
  } catch (error) {
    console.error("[IQ][CategoryForm] Error deleting category:", error);
    closeModal('deletingModal');
  }
};

const openPreviewSaveModal = () => {
  if (!isCategoryValid.value) return;

  const isPublishing = isPublish.value;
  const buttonName = isPublishing ? "Publish" : "Save";
  const description = isPublishing
    ? "Do you want to publish your updates?"
    : "Do you want to save your updates?";
  const imageName = isPublishing ? publishVariantIcon : savePopupImage;

  openModal({
    name: "CategorySaveModal",
    props: {
      closeModal: () => closeModal("CategorySaveModal"),
      saveAndPublishFunction: saveButtonClickAsync,
      availableLang: [],
      buttonName,
      description,
      imageName,
      slugCheckConfirm: false,
      hasSlugExist: hasSlugExist.value,
    },
  });
};

const saveButtonClickAsync = async () => {
  try {
    closeModal("CategorySaveModal");

    openModal({
      name: "CategorySavingModal",
      props: {
        status: isPublish.value ? "publishing" : "saving",
      },
    });

    if (props.isEdit && categoryISIN.value) {
      await updateCategoryWithVariants();
    } else {
      await createCategoryWithVariants();
    }

    closeModal("CategorySavingModal");

    hasChanges.value = false;

    setTimeout(() => {
      router.push("/category");
    }, 100);
  } catch (error) {
    console.error("[IQ][CategoryForm] Error saving category:", error);
    closeModal("CategorySavingModal");
  }
};

const getCategoryISINAsync = async () => {
  try {
    const response = await isinStore.getNewISINsAsync(lang.value, { entity: "recipeGroup" });
    newIsin.value = response?.isin || "";
  } catch (error) {
    console.error("[IQ][CategoryForm] Failed to fetch ISIN:", error);
  }
};

const createCategoryWithVariants = async () => {
  const currentLang = userDataStore.getDefaultLang;

  const defaultVariantData = {
    name: categoriesName.value.trim(),
    lang: currentLang,
  };

  const allVariants = [defaultVariantData, ...recipeVariantList.value];

  const payload = {
    isin: newIsin.value,
    type: "category",
    data: updatedRecipeVariantList(allVariants),
    image: {
      [currentLang]: imageResponseUrl.value ? imageResponseUrl.value.replace(/\?.*/, "") : image.value,
    },
    slug: categoriesSlug.value
      ? { [currentLang]: categoriesSlug.value }
      : undefined,
  };

  if (!payload.image) delete payload.image;
  if (!payload.slug) delete payload.slug;

  const response = await categoryStore.postCategoryAsync(payload, currentLang);

  if (response?.entity?.isin && selectedCategoryRecipe.value.length > 0) {
    categoryISIN.value = response.entity.isin;
    await postCategoryRecipeAsync(selectedCategoryRecipe.value);

    if (categoryPromotedRecipes.value.length > 0) {
      await savePromotedRecipeAsync();
    }
  }

  if (isPublish.value && response?.entity?.isin) {
    await patchPublishCategoryAsync(response.entity.isin);
  }
};

const updateCategoryWithVariants = async () => {
  const currentLang = userDataStore.getDefaultLang;

  const defaultVariantData = {
    name: categoriesName.value.trim(),
    lang: currentLang,
  };

  const allVariants = [defaultVariantData, ...recipeVariantList.value];

  let payload = {
    isin: categoryISIN.value,
    slug: {
      [lang.value]: categoriesSlug.value?.trim() || "",
    },
    type: "category",
    data: updatedRecipeVariantList(allVariants),
    image: {
      [lang.value]: imageResponseUrl.value ? imageResponseUrl.value.replace(/\?.*/, "") : image.value,
    },
  };
  payload = await setPayLoadWithVariantAsync(payload);
  payload = await setPayLoadWithVariantSlugAsync(payload);
  if (payload?.slug?.[lang.value] === "") {
    delete payload.slug;
  }

  await categoryStore.postCategoryAsync(payload, currentLang);

  if (saveRemovedCategoryVariants.value?.length) {
    await deleteVariantAsync();
  }

  if (selectedCategoryRecipe.value?.length) {
    await postCategoryRecipeAsync(selectedCategoryRecipe.value);

    if (isAbortedCheckingOperationStatus.value) {
      throw new Error("Operation was aborted");
    }
  }

  await savePromotedRecipeAsync();

  if (recipeMatchesIsinsRemove.value.length > 0) {
    await removeCategoryRecipeAsync();

    if (isAbortedCheckingOperationStatus.value) {
      throw new Error("Operation was aborted");
    }
  }

  await patchPublishCategoryAsync(categoryISIN.value);
};

const updatedRecipeVariantList = (variantList) => {
  const updatedVariantList = {};

  variantList.forEach((variant) => {
    if (variant.name && variant.lang) {
      updatedVariantList[variant.lang] = {
        name: variant.name.trim(),
      };
    }
  });
  finalSelectedLanguage.value = Object.keys(updatedVariantList);

  return updatedVariantList;
};

const setPayLoadWithVariantAsync = async (payload) => {
  if (payload.image && payload.image[lang.value]) {
    payload.image = await setLanguageVariant(payload.image);
  }
  return payload;
};

const setPayLoadWithVariantSlugAsync = async (payload) => {
  if (payload.slug && payload.slug[lang.value]) {
    payload.slug = await setLanguageVariant(payload.slug);
  }
  return payload;
};

const setLanguageVariant = (variantList) => {
  const copyObjectData = [];
  if (
    finalSelectedLanguage.value.length > 0 &&
    variantList &&
    variantList[lang.value]
  ) {
    finalSelectedLanguage.value.forEach((item) => {
      copyObjectData.push({ [item]: variantList[lang.value] });
    });
  }

  return Object.assign({}, ...copyObjectData);
};

const addRecipeToCategory = () => {
  recipesWereAddedInModal.value = false;

  openModal({
    name: "CategoryAddRecipeModal",
    props: {
      closeModal: () => handleAddRecipeModalClose(),
      recipeDataForCategories: recipeDataForCategories.value || [],
      categoryPromotedRecipes: categoryPromotedRecipes.value || [],
      selectedCategoryRecipe: selectedCategoryRecipe.value || [],
      recipesAfterPageChange: recipesAfterPageChange.value || [],
      recipeMatchesIsinsTagRemove: recipeMatchesIsinsTagRemove.value || [],
      removeRecipeList: [],
      removeRecipeTagList: [],
      isEditCategories: props.isEdit,
      isAddCategory: !props.isEdit,
      isPageLoading: false,
      isAddTag: false,
      isEditTag: false,
      preventEnterAndSpaceKeyPress: (event) => {
        if (event.key === "Enter" || event.key === " ") {
          event.preventDefault();
        }
      },
      onRecipeAdded: (addedRecipes) => {
        handleRecipeAdded(addedRecipes);
        hasChanges.value = true;
      },
    },
  });
};

const handleAddRecipeModalClose = async () => {
  closeModal("CategoryAddRecipeModal");
  if (props.isEdit && categoryISIN.value && recipesWereAddedInModal.value) {
    const langValue = userDataStore.getDefaultLang;
    await Promise.all([
      getRecipeDataForCategoriesAsync(categoryISIN.value, langValue),
      getPromotedRecipesForCategoriesAsync(categoryISIN.value, langValue),
    ]);
  }
  recipesWereAddedInModal.value = false;
};

const handleRecipeAdded = async (addedRecipes) => {
  if (addedRecipes && addedRecipes.length > 0) {
    try {
      let recipesActuallyAdded = false;

      addedRecipes.forEach((recipe) => {
        const existsInRegular = recipeDataForCategories.value.find(
          (r) => r.isin === recipe.isin
        );
        const existsInPromoted = categoryPromotedRecipes.value.find(
          (r) => r.isin === recipe.isin
        );

        if (!existsInRegular && !existsInPromoted) {
          const formattedRecipe = {
            isin: recipe.isin,
            title: recipe.title?.[lang.value] || recipe.title || "",
            totalTime: recipe.time?.total || "",
            ingredientCount: recipe.ingredients?.[lang.value]?.length || 0,
            image:
              recipe.media?.[lang.value]?.image ||
              recipe.media?.[lang.value]?.externalImageUrl ||
              defaultImage,
            media: recipe.media,
            time: recipe.time,
            ingredients: recipe.ingredients,
            isPromoted: false,
          };

          recipeDataForCategories.value.unshift(formattedRecipe);

          if (!selectedCategoryRecipe.value.includes(recipe.isin)) {
            selectedCategoryRecipe.value.push(recipe.isin);
          }

          if (!addedIsins.value.includes(recipe.isin)) {
            addedIsins.value.push(recipe.isin);
          }

          recipesActuallyAdded = true;
        }
      });

      if (recipesActuallyAdded) {
        hasChanges.value = true;
        recipeForCategoriesTotal.value = recipeDataForCategories.value.length;
        recipesWereAddedInModal.value = true;
      }
    } catch (error) {
      console.error(
        "[IQ][CategoryForm] Error adding recipes to category:",
        error
      );
    }
  }
};

const postCategoryRecipeAsync = async (recipeIsins) => {
  try {
    const payload = {
      sourceId: $keys.KEY_NAMES.SOURCE_ID,
      data: {
        action: "add",
        isin: categoryISIN.value,
        targets: recipeIsins,
      },
    };

    const response = await categoryStore.postCategoryRecipeAsync(payload);

    if (response?.opId) {
      await checkOperationStatusAsync(response.opId);
    }

    return response;
  } catch (error) {
    console.error(
      "[IQ][CategoryForm] Error in postCategoryRecipeAsync:",
      error
    );
    throw error;
  }
};

const savePromotedRecipeAsync = async () => {
  const promotedData = categoryPromotedRecipes.value
    ? categoryPromotedRecipes.value.map((recipe) => recipe.isin)
    : [];

  const payload = {
    isin: categoryISIN.value,
    targetIsin: categoryISIN.value,
    campaignType: "categoryRecipeSuggestion",
    promotedRecipeIsins: [...new Set(promotedData)],
    filteredRecipeIsins: filteredRecipeIsins.value || [],
    preview: false,
  };

  try {
    const currentLang = userDataStore.getDefaultLang;
    await categoryStore.saveRecipeCampaignDataAsync(payload, currentLang);
  } catch (error) {
    console.error("[IQ][CategoryForm] Error saving promoted recipes:", error);
    throw error;
  }
};

const checkOperationStatusAsync = async (operationId) => {
  let timeout = 0;
  const states = [$keys.KEY_NAMES.DONE, $keys.KEY_NAMES.FAILED];
  const abortedTimer = setTimeout(
    () => (isAbortedCheckingOperationStatus.value = true),
    1000 * 60 * 2
  );

  while (true) {
    await new Promise((resolve) => setTimeout(resolve, timeout));
    await getOperationStatusAsync(operationId);

    if (
      states.includes(categoryStore.getOperationStatus.state) ||
      isAbortedCheckingOperationStatus.value
    ) {
      if (abortedTimer) {
        clearTimeout(abortedTimer);
      }
      break;
    }

    timeout = 1000;
  }
};

const getOperationStatusAsync = async (operationId) => {
  await categoryStore.getOperationStatusAsync(operationId);
};

const removeCategoryRecipeAsync = async () => {
  const isin = route.params.isin;
  const payload = {
    sourceId: $keys.KEY_NAMES.SOURCE_ID,
    data: {
      action: "remove",
      isin: isin,
      targets: recipeMatchesIsinsRemove.value,
    },
  };

  try {
    const response = await categoryStore.postCategoryRecipeAsync(payload);
    const operationId = response.opId;
    await checkOperationStatusAsync(operationId);
  } catch (error) {
    console.error("[IQ][CategoryForm] Error removing category recipes:", error);
    throw error;
  }
};

const patchPublishCategoryAsync = async (isin) => {
  if (isin && isCategoriesStatus.value) {
    const payload = {
      status: isCategoriesStatus.value,
    };
    try {
      await categoryStore.patchCategoryAsync(payload, isin);
    } catch (error) {
      console.error(
        "[IQ][CategoryForm] Error updating category status:",
        error
      );
      throw error;
    }
  }
};

const selectRecipes = () => {
  isSelectionEnabled.value = true;
  recipeDataForCategories.value.forEach((recipe) => {
    if (recipe.isSelectedToDelete === undefined) {
      recipe.isSelectedToDelete = false;
    }
  });
  if (selectedProductsAcrossPages.value.length > 0) {
    restorePageSelections();
  }
};

const selectAllMatches = () => {
  selectionOfRecipes.value[0].isSelected =
    !selectionOfRecipes.value[0].isSelected;

  const updatedRecipes = recipeDataForCategories.value.map((item) => ({
    ...item,
    isSelectedToDelete: selectionOfRecipes.value[0].isSelected,
  }));

  recipeDataForCategories.value = updatedRecipes;

  if (selectionOfRecipes.value[0].isSelected) {
    selectedProducts.value = [...updatedRecipes.filter(item => item.isSelectedToDelete)];
  } else {
    selectedProducts.value = [];
    const currentPageIsins = recipeDataForCategories.value.map(item => item.isin);
    selectedProductsAcrossPages.value = selectedProductsAcrossPages.value.filter(
      item => !currentPageIsins.includes(item.isin)
    );
  }

  updateSelectionCount();
};

const tableRowClickAction = (data, index) => {
  if (isSelectionEnabled.value) {
    handleCheckboxClick(index, data, null);
  }
};

const handleCheckboxClick = async (index, data, event) => {
  if (event) {
    event.stopPropagation();
    event.preventDefault();
  }

  if (isSelectionEnabled.value && data) {
    let actualIndex = index;
    if (actualIndex === undefined || actualIndex === null) {
      actualIndex = recipeDataForCategories.value.findIndex(
        (item) => item.isin === data.isin
      );
    }

    if (
      actualIndex >= 0 &&
      actualIndex < recipeDataForCategories.value.length
    ) {
      const item = recipeDataForCategories.value[actualIndex];

      const wasSelected = item.isSelectedToDelete || false;
      item.isSelectedToDelete = !wasSelected;

      recipeDataForCategories.value = [...recipeDataForCategories.value];

      if (!wasSelected) {
        const exists = selectedProducts.value.find((p) => p.isin === item.isin);
        if (!exists) {
          selectedProducts.value.push(item);
        }
      } else {
        selectedProducts.value = selectedProducts.value.filter(
          (insideItem) => insideItem.isin !== item.isin
        );
        selectedProductsAcrossPages.value = selectedProductsAcrossPages.value.filter(
          (insideItem) => insideItem.isin !== item.isin
        );
      }

      await nextTick();
      updateSelectionCount();

      await nextTick();

      checkSelected();
    } else {
      console.error(
        "[IQ][CategoryForm] Could not find item with ISIN:",
        data.isin,
        "in recipeDataForCategories"
      );
    }
  }
};

const checkSelected = () => {
  let count = 0;
  recipeDataForCategories.value.forEach((item) => {
    if (item.isSelectedToDelete) {
      count += 1;
    }
  });
  selectionOfRecipes.value[0].isSelected =
    count === recipeDataForCategories.value.length;
};

const cancelSelect = () => {
  isSelectionEnabled.value = false;
  selectedProducts.value = [];
  selectedProductsAcrossPages.value = [];
  selectionOfRecipes.value[0].isSelected = false;

  if (recipeDataForCategories.value.length > 0) {
    const updatedRecipes = recipeDataForCategories.value.map((item) => ({
      ...item,
      isSelectedToDelete: false,
    }));
    recipeDataForCategories.value = updatedRecipes;
  }

  updateSelectionCount();
};

const removeAllSelected = () => {
  const updatedRecipes = recipeDataForCategories.value.map((item) => ({
    ...item,
    isSelectedToDelete: false,
  }));
  recipeDataForCategories.value = updatedRecipes;

  selectionOfRecipes.value[0].isSelected = false;
  selectedProducts.value = [];
  selectedProductsAcrossPages.value = [];

  updateSelectionCount();
};

const deleteSelect = () => {
  if (checkSelectedRecipes.value > 0) {
    openModal({
      name: "CategorySelectDeleteModal",
      props: {
        closeModal: () => closeModal("CategorySelectDeleteModal"),
        productInfoTitle: "Remove Recipe?",
        productDescriptionOne:
          "Are you sure you want to remove this recipe from the",
        productDescriptionTwo: "category?",
        deleteItem: deleteSelectProductMatches,
        availableLanguage: 0,
        buttonText: "Remove",
      },
    });
  } else {
    console.warn("[IQ][CategoryForm] No recipes selected for deletion");
  }
};

const deleteSelectProductMatches = async () => {
  try {
    const selectedIsinsToRemove = [];

    recipeDataForCategories.value.forEach((recipe) => {
      if (recipe.isSelectedToDelete) {
        selectedIsinsToRemove.push(recipe.isin);
        removeRecipeList.value.push(recipe);
      }
    });
    selectedProductsAcrossPages.value.forEach((recipe) => {
      if (!selectedIsinsToRemove.includes(recipe.isin)) {
        selectedIsinsToRemove.push(recipe.isin);
        removeRecipeList.value.push(recipe);
      }
    });

    if (selectedIsinsToRemove.length === 0) {
      console.warn("[IQ][CategoryForm] No recipes selected for removal");
      closeModal("CategorySelectDeleteModal");
      return;
    }
    recipesAfterPageChange.value = recipesAfterPageChange.value.filter(
      (data) => {
        return !selectedIsinsToRemove.includes(data.isin);
      }
    );

    recipeDataForCategories.value = recipeDataForCategories.value.filter(
      (data) => {
        return !selectedIsinsToRemove.includes(data.isin);
      }
    );

    recipeMatchesIsinsRemove.value.push(...selectedIsinsToRemove);

    selectedCategoryRecipe.value = selectedCategoryRecipe.value.filter(
      (isin) => !selectedIsinsToRemove.includes(isin)
    );

    addedIsins.value = addedIsins.value.filter(
      (isin) => !selectedIsinsToRemove.includes(isin)
    );

    hasChanges.value = true;

    closeModal("CategorySelectDeleteModal");
    selectedProducts.value = [];
    selectedProductsAcrossPages.value = [];
    selectionOfRecipes.value[0].isSelected = false;
    isSelectionEnabled.value = false;

    if (props.isEdit && categoryISIN.value) {
      const langValue = userDataStore.getDefaultLang;

      const currentPage = Math.floor(fromRecipe.value / sizeRecipe.value) + 1;
      const remainingItems = recipeDataForCategories.value.length;
      const maxPage = Math.ceil(remainingItems / sizeRecipe.value);

      if (currentPage > maxPage && maxPage > 0) {
        fromRecipe.value = (maxPage - 1) * sizeRecipe.value;
      } else if (remainingItems === 0 && fromRecipe.value > 0) {
        fromRecipe.value = Math.max(0, fromRecipe.value - sizeRecipe.value);
      }

      await getRecipeDataForCategoriesAsync(categoryISIN.value, langValue);
    } else {
      recipeForCategoriesTotal.value = recipeDataForCategories.value.length;
    }
  } catch (error) {
    console.error("[IQ][CategoryForm] Error removing selected recipes:", error);
    closeModal("CategorySelectDeleteModal");
  }
};

watch(
  () => getSearchQuery(),
  (newSearchQuery, oldSearchQuery) => {
    if (newSearchQuery !== oldSearchQuery) {
      searchcopy.value = newSearchQuery || "";

      if (props.isEdit && categoryISIN.value) {
        const langValue = userDataStore.getDefaultLang;
        getRecipeDataForCategoriesAsync(categoryISIN.value, langValue);
      }
    }
  },
  { immediate: false }
);

const getRecipeImage = (recipe) => {
  if (!recipe) return defaultImage;
  if (recipe.image) {
    return recipe.image;
  }

  if (recipe.media) {
    const currentLang = userDataStore.getDefaultLang;
    if (recipe?.media?.image) {
      return recipe.media.image;
    }

    if (recipe.media[currentLang]?.image) {
      return recipe.media[currentLang].image;
    }

    if (recipe.media[currentLang]?.thumbnailImageUrl) {
      return recipe.media[currentLang].thumbnailImageUrl;
    }

    if (recipe.media[currentLang]?.imageList?.length > 0) {
      return recipe.media[currentLang].imageList[0].url;
    }

    const availableLanguages = Object.keys(recipe.media);
    for (const lang of availableLanguages) {
      if (recipe.media[lang]?.image) {
        return recipe.media[lang].image;
      }
      if (recipe.media[lang]?.thumbnailImageUrl) {
        return recipe.media[lang].thumbnailImageUrl;
      }
      if (recipe.media[lang]?.imageList?.length > 0) {
        return recipe.media[lang].imageList[0].url;
      }
    }
  }

  return defaultImage;
};

const getRecipeTime = (recipe) => {
  if (!recipe) return "";

  if (recipe.totalTime && typeof recipe.totalTime === "string") {
    return recipe.totalTime;
  }

  if (recipe.time?.total) {
    return parseDurationString(recipe.time.total);
  }

  if (recipe.time) {
    const { cook, prep } = recipe.time;
    if (cook && prep) {
      const cookTime = parseDurationString(cook);
      const prepTime = parseDurationString(prep);
      if (cookTime && prepTime) {
        return `${prepTime} prep + ${cookTime} cook`;
      }
    }

    if (cook) {
      return parseDurationString(cook);
    }

    if (prep) {
      return parseDurationString(prep);
    }
  }

  return "none";
};

const getRecipeTitle = (recipe) => {
  if (!recipe) return "";

  const currentLang = userDataStore.getDefaultLang || "fr-FR";

  if (recipe.title && typeof recipe.title === "string") {
    return recipe.title;
  }

  if (recipe.title[currentLang]) {
    return recipe.title[currentLang];
  }

  const availableLanguages = Object.keys(recipe.title);
  for (const lang of availableLanguages) {
    if (recipe.title[lang]) {
      return recipe.title[lang];
    }
  }

  return "";
};

const getIngredientCount = (recipe) => {
  if (!recipe) return 0;

  const currentLang = userDataStore.getDefaultLang || "fr-FR";

  if (recipe.ingredients && typeof recipe.ingredients === "object") {
    if (
      recipe.ingredients[currentLang] &&
      Array.isArray(recipe.ingredients[currentLang])
    ) {
      return recipe.ingredients[currentLang].length;
    }

    if (Array.isArray(recipe.ingredients)) {
      return recipe.ingredients.length;
    }

    const availableLanguages = Object.keys(recipe.ingredients);
    for (const lang of availableLanguages) {
      if (Array.isArray(recipe.ingredients[lang])) {
        return recipe.ingredients[lang].length;
      }
    }
  }

  return 0;
};

const getPromotedRecipeActions = (recipe) => {
  return [
    {
      isDisable: false,
      isInactive: false,
      key: [RECIPE_ACTION_CASE.PREVIEW, recipe.isin],
      label: "Preview",
    },
    {
      isDisable: false,
      isInactive: false,
      key: [RECIPE_ACTION_CASE.UNPROMOTE, recipe.isin],
      label: "Unpromote",
    },
  ];
};

const getCategoryRecipeActions = (recipe) => {
  return [
    {
      isDisable: false,
      isInactive: false,
      key: [RECIPE_ACTION_CASE.PREVIEW, recipe.isin],
      label: "Preview",
    },
    {
      isDisable: false,
      isInactive: false,
      key: [RECIPE_ACTION_CASE.REMOVE, recipe.isin],
      label: "Remove",
    },
  ];
};

const handleRecipeAction = ([actionType, recipeIsin]) => {
  const recipe = [
    ...categoryPromotedRecipes.value,
    ...recipeDataForCategories.value,
  ].find((r) => r.isin === recipeIsin);

  if (!recipe) {
    console.error(
      "[IQ][CategoryForm] Recipe not found for action:",
      actionType,
      recipeIsin
    );
    return;
  }

  switch (actionType) {
    case RECIPE_ACTION_CASE.PREVIEW:
      openModal({
        name: "CategoryRecipePreviewModal",
        props: {
          recipeIsin: recipeIsin,
          checkRecipePreviewVideo: () => {},
        },
      });
      break;
    case RECIPE_ACTION_CASE.UNPROMOTE:
      removePromotedRecipe(recipe);
      break;
    case RECIPE_ACTION_CASE.REMOVE:
      removeRecipeData.value = recipe;
      openModal({
        name: "CategoryDeleteModal",
        props: {
          closeModal: () => closeModal("CategoryDeleteModal"),
          productInfoTitle: "Remove Recipe?",
          productDescriptionOne:
            "Are you sure you want to remove this recipe from the category?",
          deleteItem: removeRecipeFromCategory,
          availableLanguage: 0,
          buttonText: $t("BUTTONS.REMOVE_BUTTON"),
        },
      });
      break;
    default:
      console.warn("[IQ][CategoryForm] Unknown action type:", actionType);
  }
};

const handleRecipesPageChange = (page) => {
  if (isSelectionEnabled.value) {
    const currentPageSelectedItems = recipeDataForCategories.value.filter(
      (item) => item.isSelectedToDelete === true
    );
    currentPageSelectedItems.forEach((item) => {
      const exists = selectedProductsAcrossPages.value.find((p) => p.isin === item.isin);
      if (!exists) {
        selectedProductsAcrossPages.value.push(item);
      }
    });
    selectedProducts.value = [];
    selectionOfRecipes.value[0].isSelected = false;
  }

  scrollToTop();
  fromRecipe.value = (page - 1) * sizeRecipe.value;
  if (props.isEdit && categoryISIN.value) {
    const langValue = userDataStore.getDefaultLang;
    getRecipeDataForCategoriesAsync(categoryISIN.value, langValue);
  }
};

const removeRecipeFromCategory = async () => {
  const index = recipeDataForCategories.value.findIndex(
    (r) => r.isin === removeRecipeData.value.isin
  );
  if (index > -1) {
    recipeDataForCategories.value.splice(index, 1);

    const isinIndex = addedIsins.value.indexOf(removeRecipeData.value.isin);
    if (isinIndex > -1) {
      addedIsins.value.splice(isinIndex, 1);
    }

    if (!recipeMatchesIsinsRemove.value.includes(removeRecipeData.value.isin)) {
      recipeMatchesIsinsRemove.value.push(removeRecipeData.value.isin);
    }

    hasChanges.value = true;

    if (props.isEdit && categoryISIN.value) {
      const langValue = userDataStore.getDefaultLang;

      const remainingItems = recipeDataForCategories.value.length;
      if (remainingItems === 0 && fromRecipe.value > 0) {
        fromRecipe.value = Math.max(0, fromRecipe.value - sizeRecipe.value);
      }

      await getRecipeDataForCategoriesAsync(categoryISIN.value, langValue);
    } else {
      recipeForCategoriesTotal.value = recipeDataForCategories.value.length;
    }
  }
  closeModal("CategoryDeleteModal");
};

const removePromotedRecipe = (recipe) => {
  const index = categoryPromotedRecipes.value.findIndex(
    (r) => r.isin === recipe.isin
  );
  if (index > -1) {
    const unpromotedRecipe = { ...recipe, isPromoted: false };
    categoryPromotedRecipes.value.splice(index, 1);

    recipeDataForCategories.value.unshift(unpromotedRecipe);

    categoryPromotedRecipesTotal.value -= 1;
    recipeForCategoriesTotal.value += 1;

    if (!totalPromotedRemovedIsin.value.includes(recipe.isin)) {
      totalPromotedRemovedIsin.value.push(recipe.isin);
    }

    hasChanges.value = true;
  }
  triggerLoading($keys.KEY_NAMES.RECIPE_UNPROMOTED);
};

const promoteRecipe = (recipe) => {
  const index = recipeDataForCategories.value.findIndex(
    (r) => r.isin === recipe.isin
  );
  if (index > -1) {
    const promotedRecipe = { ...recipe, isPromoted: true };
    recipeDataForCategories.value.splice(index, 1);
    categoryPromotedRecipes.value.push(promotedRecipe);

    categoryPromotedRecipesTotal.value += 1;
    recipeForCategoriesTotal.value -= 1;

    const removedIndex = totalPromotedRemovedIsin.value.indexOf(recipe.isin);
    if (removedIndex > -1) {
      totalPromotedRemovedIsin.value.splice(removedIndex, 1);
    }

    hasChanges.value = true;

    if (props.isEdit && categoryISIN.value) {
      const langValue = userDataStore.getDefaultLang;
      getRecipeDataForCategoriesAsync(categoryISIN.value, langValue);
    }
  }
  scrollToTop();
  triggerLoading($keys.KEY_NAMES.RECIPE_PROMOTED);
};

const getPromotedRecipesForCategoriesAsync = async (isin, langParam) => {
  try {
    if (!isin) {
      console.error("[IQ][CategoryForm] Missing required parameter: isin");
      return;
    }

    const lang = langParam || userDataStore.getDefaultLang;

    const response = await categoryStore.getPromotedRecipesForCategoriesAsync(isin, lang);
    if (response) {
      if (Array.isArray(response)) {
        categoryPromotedRecipes.value = response.map((recipe) => ({
          ...recipe,
          isPromoted: true,
        }));
        categoryPromotedRecipesTotal.value = response.length;
      } else if (
        response.promotedRecipes &&
        Array.isArray(response.promotedRecipes)
      ) {
        categoryPromotedRecipes.value = response.promotedRecipes.map(
          (recipe) => ({
            ...recipe,
            isPromoted: true,
          })
        );
        categoryPromotedRecipesTotal.value =
          response.total || response.promotedRecipes.length;
      } else if (response.results && Array.isArray(response.results)) {
        categoryPromotedRecipes.value = response.results.map((recipe) => ({
          ...recipe,
          isPromoted: true,
        }));
        categoryPromotedRecipesTotal.value =
          response.total || response.results.length;
      } else if (response.data && Array.isArray(response.data)) {
        categoryPromotedRecipes.value = response.data.map((recipe) => ({
          ...recipe,
          isPromoted: true,
        }));
        categoryPromotedRecipesTotal.value =
          response.total || response.data.length;
      } else {
        categoryPromotedRecipes.value = [];
        categoryPromotedRecipesTotal.value = 0;
      }
    } else {
      categoryPromotedRecipes.value = [];
      categoryPromotedRecipesTotal.value = 0;
    }
  } catch (error) {
    console.error("[IQ][CategoryForm] Error loading promoted recipes:", error);
    categoryPromotedRecipes.value = [];
  }
};

const getRecipeDataForCategoriesAsync = async (isin, langParam) => {
  try {
    if (!isin) {
      console.error("[IQ][CategoryForm] Missing required parameter: isin");
      return;
    }

    const lang = langParam || userDataStore.getDefaultLang;

    const promotedRecipeIsins = categoryPromotedRecipes.value.map(
      (recipe) => recipe.isin
    );

    const allExcludingIsins = [
      ...totalPromotedRemovedIsin.value,
      ...promotedRecipeIsins,
      ...recipeMatchesIsinsRemove.value,
    ].filter(Boolean);

    const searchQuery = getSearchQuery();
    let finalSearchQuery = "";

    if (searchQuery && searchQuery.trim() !== "") {
      finalSearchQuery = searchQuery.trim();
      searchcopy.value = finalSearchQuery;
    } else if (searchcopy.value && searchcopy.value.trim() !== "") {
      finalSearchQuery = searchcopy.value.trim();
    }

    const payload = {
      country: lang.split("-")[1],
      q: finalSearchQuery,
      excludingIsins: allExcludingIsins.join(","),
      groupsIncludingIsins: addedIsins.value.join(","),
      groups: isin,
      from: fromRecipe.value,
      size: sizeRecipe.value,
      sort: "lastMod",
    };

    const response = await categoryStore.getRecipeForCategoriesAsync(payload);
    if (response?.results && Array.isArray(response.results)) {
      recipeDataForCategories.value = response.results.map((recipe) => ({
        ...recipe,
        isPromoted: false,
      }));
      recipeForCategoriesTotal.value = response.total || 0;
      if (isSelectionEnabled.value) {
        restorePageSelections();
      }
    } else {
      recipeDataForCategories.value = [];
      recipeForCategoriesTotal.value = 0;
    }
  } catch (error) {
    console.error(
      "[IQ][CategoryForm] Error loading recipes for categories:",
      error
    );
    recipeDataForCategories.value = [];
    recipeForCategoriesTotal.value = 0;
  }
};
const restorePageSelections = () => {
  if (selectedProductsAcrossPages.value.length > 0) {
    const crossPageIsins = selectedProductsAcrossPages.value.map(item => item.isin);
    recipeDataForCategories.value = recipeDataForCategories.value.map((recipe) => {
      if (crossPageIsins.includes(recipe.isin)) {
        return { ...recipe, isSelectedToDelete: true };
      }
      return recipe;
    });
    selectedProducts.value = recipeDataForCategories.value.filter(
      (recipe) => recipe.isSelectedToDelete === true
    );
    const currentPageIsins = recipeDataForCategories.value.map(item => item.isin);
    selectedProductsAcrossPages.value = selectedProductsAcrossPages.value.filter(
      item => !currentPageIsins.includes(item.isin)
    );
    updateSelectionCount();
    checkSelected();
  }
};

const getSearchConfigAsync = async () => {
  try {
    const response = await editSearchStore.getEditSearchAsync();
    if (response) {
      searchConfig.value = response;
    } else {
      searchConfig.value = {};
    }
  } catch (error) {
    console.error("[IQ][CategoryForm] Error loading search config:", error);
    searchConfig.value = {};
  }
};

const getEditCategoryListAsync = async (isin, lang) => {
  try {
    const response = await fetchCategoryGroupData(isin, lang);

    if (response) {
      processCategoryGroupData(response);
      updateCategoryDetails(response, lang);
    }
  } catch (error) {
    console.error(
      "[IQ][CategoryForm] Error in getEditCategoryListAsync:",
      error
    );
  }
};

const fetchCategoryGroupData = async (isin, lang) => {
  try {
    return await categoryStore.getEditCategoryGroupListAsync(isin, lang, "category");
  } catch (error) {
    console.error("[IQ][CategoryForm] Error in fetchCategoryGroupData:", error);
    return null;
  }
};

const processCategoryGroupData = (response) => {
  const { data } = response;
  selectedDefaultLang.value.forEach((language) => {
    if (data?.[language] && language !== lang.value) {
      const newVariantData = {
        name: data[language].name,
        lang: language,
      };

      recipeVariantList.value.push(newVariantData);
      initiallyVariantSupported.value.push(newVariantData);
    }
  });
};

const updateCategoryDetails = (response, lang) => {
  const { data, isin, status, state, slug } = response;

  categoryISIN.value = isin || "";
  isCategoriesStatus.value = status || "";
  categoriesState.value = state || "";
  categoriesSlug.value = slug?.[lang] || "";

  isPublish.value = status === "active";

  const langData = data?.[lang];
  if (langData) {
    categoriesName.value = langData.name || "";
    const categoryImage = langData.image || defaultImage;

    if (categoryImage) {
      image.value = categoryImage;
    }
  }

  const categoryData = categoryStore.getCategoryData;
  if (categoryData) {
    if (categoryData.name && !categoriesName.value) {
      categoriesName.value = categoryData.name;
    }
    if (categoryData.slug && !categoriesSlug.value) {
      categoriesSlug.value = categoryData.slug;
    }
    if (categoryData.image && !image.value) {
      image.value = categoryData.image;
    }
  }
};

const openRecipeVariantPopUp = () => {
  hasRecipeVariantLanguagePopUp.value = true;
  hasRecipeVariantLanguageResult.value = false;
  hasDisableSelectLanguageButton.value = false;
  recipeVariantLanguage.value = "";
};

const setRecipeVariantLanguageMatches = (value, index) => {
  recipeVariantLanguage.value = value.language;
  recipeVariantLanguageIndex.value = index;
  hasRecipeVariantLanguageResult.value = false;
  hasDisableSelectLanguageButton.value = true;

  recipeVariantLanguageList.value.forEach((data, idx) => {
    if (data.language === recipeVariantLanguage.value) {
      recipeVariantLanguageList.value.splice(idx, 1);
      recipeVariantLanguageList.value.unshift(data);
    }
  });
};

const showRecipeVariantLanguageMatches = () => {
  hasRecipeVariantLanguageResult.value = !hasRecipeVariantLanguageResult.value;
};

const nextCategoryVariantNameModalPopUp = (item) => {
  if (item === "") {
    recipeVariantSelectedLanguage.value =
      recipeVariantLanguageList.value[0]?.language || "";
    recipeVariantLanguage.value =
      recipeVariantLanguageList.value[0]?.language || "";
  } else {
    recipeVariantSelectedLanguage.value = item;
  }
  hasRecipeVariantLanguagePopUp.value = false;
  isAddVariantCategoryNamePopUp.value = true;
  hasRecipeVariantLanguageResult.value = false;
};

const backToSelectLanguageVariantPopUp = () => {
  isAddVariantCategoryNamePopUp.value = false;
  hasRecipeVariantLanguagePopUp.value = true;
};

const addRecipeVariant = (item) => {
  variantName.value = item;
  if (variantName.value !== "") {
    const newVariantData = {
      name: item.trim(),
      lang: recipeVariantLanguage.value,
    };
    recipeVariantList.value.push(newVariantData);
    hasChanges.value = true;
    isAddVariantCategoryNamePopUp.value = false;
    variantName.value = "";

    recipeVariantLanguageList.value = recipeVariantLanguageList.value.filter(
      (data) => data.language !== recipeVariantLanguage.value
    );

    saveRemovedCategoryVariants.value =
      saveRemovedCategoryVariants.value.filter(
        (data) => data !== recipeVariantLanguage.value
      );

    getCategoryAssociations();
  }
};

const displayLanguageCode = (item) => {
  if (item) {
    const arr = item.split("-");
    return arr[0].toUpperCase();
  }
  return "";
};
const deleteCategoryVariantAsync = () => {
  recipeVariantList.value.splice(categoryVariantDataIndex.value, 1);
  hasChanges.value = true;
  closeModal("CategoryDeleteModal");
};

const deleteCategoryVariant = (categoryVariant, index) => {
  categoryVariantDataIndex.value = index;
  openModal({
    name: "CategoryDeleteModal",
    props: {
      closeModal: () => closeModal("CategoryDeleteModal"),
      productInfoTitle: "Remove Category Variant?",
      productDescriptionOne:
        "Are you sure you want to remove this variant from the category?",
      deleteItem: deleteCategoryVariantAsync,
      availableLanguage: 0,
      buttonText: $t("BUTTONS.REMOVE_BUTTON"),
    },
  });
  if (
    initiallyVariantSupported.value.some(
      (variant) => variant.lang === categoryVariant.lang
    )
  ) {
    saveRemovedCategoryVariants.value.push(categoryVariant.lang);
  }

  const langData = {
    language: categoryVariant.lang,
    language_name: getLanguageName(categoryVariant.lang),
    languageFlag: getLanguageFlag(categoryVariant.lang),
  };

  if (
    !recipeVariantLanguageList.value.some(
      (lang) => lang.language === categoryVariant.lang
    )
  ) {
    recipeVariantLanguageList.value.push(langData);
  }

  getCategoryAssociations();
};

const isDeleteVariantVisible = (categoryVariant) => {
  return categoryAssociations.value[categoryVariant.lang] > 0;
};

const getCategoryAssociations = async () => {
  if (!props.isEdit || !categoryISIN.value) return;

  const promises = [];
  const variantList = [];

  recipeVariantList.value.forEach((langVariant) => {
    if (langVariant.lang !== lang.value) {
      promises.push(
        categoryStore.getCategoryAssociationsAsync(
          categoryISIN.value,
          langVariant.lang,
          0,
          15
        )
          .then(() => {
            const response = categoryStore.categoryAssociations;
            const object = {
              [langVariant.lang]:
                response?.recipes?.length ||
                response?.recipeGroups?.length ||
                0,
            };
            variantList.push(object);
          })
      );
    }
  });

  await Promise.all(promises);
  categoryAssociations.value = Object.assign({}, ...variantList);
};

const getLanguageName = (langCode) => {
  const lang = langCode.split("-")[0];

  const langMap = {
    fr: "French",
    es: "Spanish",
  };

  return langMap[lang] || langCode;
};

const getLanguageFlag = (langCode) => {
  const lang = langCode.split("-")[0];

  const flagMap = {
    fr: frenchFlag,
    es: spanishFlag,
  };

  return flagMap[lang] || "";
};

const deleteVariantAsync = async () => {
  try {
    await categoryStore.deleteLanguageVariantAsync(categoryISIN.value, saveRemovedCategoryVariants.value);
  } catch (e) {
    console.error("[IQ][CategoryForm] Error deleting variants:", e);
  }
};

const inputContentChanged = () => {
  if (props.isEdit && !isInitializing.value) {
    hasChanges.value = true;
  }
};

onMounted(() => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      const lang = userDataStore.getDefaultLang;

      await initializeAvailableLanguages();
      await getCategoryISINAsync();

      if (props.isEdit) {
        const isin = route.params.isin;
        categoryISIN.value = isin;

        try {
          await categoryStore.getCategoryDataAsync(isin, lang);

          await getEditCategoryListAsync(isin, lang);

          await getPromotedRecipesForCategoriesAsync(isin, lang);

          await getRecipeDataForCategoriesAsync(isin, lang);

          await getSearchConfigAsync();

          await loadCategoryVariants(isin, lang);
          isLoading.value = false;
        } catch (error) {
          console.error(
            "[IQ][CategoryForm] Error loading category data:",
            error
          );
          isLoading.value = false;
        }
      } else {
        await getSearchConfigAsync();
      }
      isInitializing.value = false;
    }
  });
});

const initializeAvailableLanguages = async () => {
  try {
    const availableLangs = userDataStore.getAvailableLangs;

    if (!availableLangs || availableLangs.length === 0) {
      try {
        await userDataStore.fetchLangsAsync();
      } catch (error) {
        console.warn(
          "[IQ][CategoryForm] Could not fetch languages, using default set"
        );
      }
    }

    const langs = userDataStore.getAvailableLangs || [
      "en-US",
      "fr-FR",
    ];

    if (langs && langs.length > 0) {
      finalAvailableLangs.value = langs;

      const currentLang = userDataStore.getDefaultLang;
      recipeVariantLanguageList.value = langs
        .filter((langCode) => langCode !== currentLang)
        .map((langCode) => ({
          language: langCode,
          language_name: getLanguageName(langCode),
          languageFlag: getLanguageFlag(langCode),
        }));
    }
  } catch (error) {
    console.error(
      "[IQ][CategoryForm] Error initializing available languages:",
      error
    );

    const defaultLangs = ["en-US", "fr-FR"];
    finalAvailableLangs.value = defaultLangs;
    const currentLang = userDataStore.getDefaultLang || "fr-FR";

    recipeVariantLanguageList.value = defaultLangs
      .filter((langCode) => langCode !== currentLang)
      .map((langCode) => ({
        language: langCode,
        language_name: getLanguageName(langCode),
        languageFlag: getLanguageFlag(langCode),
      }));
  }
};

const loadCategoryVariants = async (isin, lang) => {
  try {
    const response = await fetchCategoryGroupData(isin, lang);
    if (response && response.data) {
      const { data } = response;
      selectedDefaultLang.value = Object.keys(data);

      selectedDefaultLang.value.forEach((language) => {
        if (data?.[language] && language !== lang) {
          const newVariantData = {
            name: data[language].name,
            lang: language,
          };

          recipeVariantList.value.push(newVariantData);
          initiallyVariantSupported.value.push(newVariantData);

          recipeVariantLanguageList.value =
            recipeVariantLanguageList.value.filter(
              (langItem) => langItem.language !== language
            );
        }
      });
      if (recipeVariantList.value.length > 0) {
        await getCategoryAssociations();
      }
    }
  } catch (error) {
    console.error("[IQ][CategoryForm] Error loading category variants:", error);
  }
};

onBeforeRouteLeave((_, __, next) => {
  if (hasChanges.value) {
    openModal({
      name: "CategoryCancelModal",
      props: {
        availableLang: [],
        isCampaignModifiedFromShoppableReview: false,
        callConfirm: () => {
          closeModal("CategoryCancelModal");
          hasChanges.value = false;
          next();
        },
        closeModal: () => {
          closeModal("CategoryCancelModal");
          next(false);
        },
      },
    });
  } else {
    next();
  }
});
</script>
