<template>
  <div
    :class="[
      containerClass,
      {
        'decrease-height': !decreaseHeight,
      },
    ]"
  >
    <div class="inner-container">
      <div class="recipe-variant-notes-text-container-dynamic">
        <div class="dynamic-hero-text-main-container">
          <div class="form-title-dynamic">
            <p class="form-title-header-dynamic">
              Text<span class="compulsory-field">*</span>
            </p>
          </div>
          <div class="preview-section-dynamic" v-if="showPreview">
            <div class="text-section-prev">
              {{ $t("DYNAMIC_HERO.HERO_PREVIEW") }}
            </div>
            <label
              class="switch"
              :class="{
                'simple-data-tooltip simple-data-tooltip-edge': !isPreviewEnabled
              }"
              :data-tooltip-text="!isPreviewEnabled && $t('DYNAMIC_HERO.HERO_PREVIEW_TEXT')"
            >
              <input
                @change="togglePreview"
                type="checkbox"
                :checked="isPreviewEnabled"
              />
              <span class="slider-round"></span>
            </label>
          </div>
        </div>
      </div>

      <div class="description-section-dynamic" id="nutrition_section_scroll">
        <textarea
          @input="onDescriptionInput"
          class="description-notes-dynamic"
          v-model="localDescription"
          id="descriptionNotes"
          maxlength="244"
        ></textarea>
        <div v-if="localDescription.length > 0" class="description-length">
          {{ localDescription.length }}/244
        </div>
      </div>

      <div class="cta-container">
        <div class="cta-text">
          CTA
          <span v-if="selectedRecipe.length && contentList[1].isChecked">
            & Recipe:
          </span>
          <span v-if="selectedCategoryData.length && contentList[1].isChecked">
            & Recipe Category:
          </span>
          <span v-if="selectedArticle.length">
            & Article:
          </span>
          <span v-if="updateShowAsterisk" class="compulsory-field">*</span>
        </div>
        <div class="cta-section">
          <div class="cta-input">
            <input
              @input="onCTAInput"
              type="text"
              class="cta-input-text"
              placeholder="Enter text"
              v-model="localctaInput"
              maxlength="40"
              id="contentCTALink"
            />
            <div v-if="localctaInput.length" class="cta-input-length">
              {{ localctaInput.length }}/40
            </div>
          </div>
          <button
            v-if="isRecipesButtonVisible && selectedRecipe.length"
            type="button"
            @click="deleteRecipe"
            class="replace-btn"
          >
            {{ $t("DYNAMIC_HERO.REMOVE_RECIPE") }}
          </button>
          <button
            v-if="selectedCategoryData.length && contentList[1].isChecked"
            type="button"
            @click="deleteCategory"
            class="red-replace-btn"
          >
            {{ $t("DYNAMIC_HERO.REMOVE_CATEGORY") }}
          </button>
          <div
            v-if="
              selectedRecipe.length == 0 && selectedCategoryData.length == 0
            "
            class="select-or-replace-recipe"
          >
            <div
              class="select-text"
              v-if="isRecipesButtonVisible && selectedRecipe.length == 0"
            >
              {{ $t("COMMON.SELECT") }}
            </div>
            <button
              type="button"
              v-if="isRecipesButtonVisible && selectedRecipe.length == 0"
              @click="selectRecipe()"
              class="select-btn-recipe"
            >
              {{ $t("DYNAMIC_HERO.CONTENT_RECIPE") }}
            </button>
            <div
              class="or-text"
              v-if="isRecipesButtonVisible && selectedRecipe.length == 0"
            >
              or
            </div>
            <button
              type="button"
              @click="addCategoryButton()"
              v-if="isRecipesButtonVisible && selectedRecipe.length == 0"
              class="select-btn-category"
            >
              {{ $t("DYNAMIC_HERO.CONTENT_CATEGORY") }}
            </button>
          </div>
          <button
            type="button"
            v-if="isArticlesButtonVisible && selectedArticle.length == 0"
            @click="selectArticle()"
            class="select-btn"
          >
            {{ $t("DYNAMIC_HERO.SELECT_ARTICLE") }}
          </button>
          <button
            type="button"
            v-if="isArticlesButtonVisible && selectedArticle.length > 0"
            @click="replaceArticle()"
            class="replace-btn-article"
          >
            {{ $t("DYNAMIC_HERO.REPLACE_ARTICLE") }}
          </button>
          <div class="error-validation" ref="validationMessageRef"></div>
        </div>
      </div>
    </div>
    <div
      v-if="selectedArticle.length && contentList[0].isChecked"
      class="article-section"
    >
      <div class="article-image">
        <img
          alt=""
          :src="
            selectedArticle[0].image ? selectedArticle[0].image : defaultImage
          "
        />
      </div>
      <div class="article-isin">
        <span class="isin-text">{{
          selectedArticle[0].uuid ? selectedArticle[0].uuid : ""
        }}</span>
      </div>
      <div class="article-name">
        <span class="name-text">{{
          selectedArticle[0].title ? selectedArticle[0].title : ""
        }}</span>
      </div>
      <div class="article-time">
        {{
          selectedArticle[0] && selectedArticle[0].lastUpdate
            ? getTime(selectedArticle[0].lastUpdate)
            : ""
        }}
      </div>
      <div class="article-delete">
        <img
          class="delete-icon"
          @click="deleteArticlePopUp()"
          alt=""
          src="@/assets/images/delete-icon.png"
        />
      </div>
    </div>
    <div
      v-if="selectedRecipe.length && contentList[1].isChecked"
      class="recipe-section"
    >
      <div class="recipe-image">
        <img
          alt="recipe"
          v-if="selectedRecipeImage"
          :src="selectedRecipeImage"
          @error="$event.target.src = defaultImage"
        />
        <img
          alt="recipe"
          v-else-if="selectedRecipe[0]?.media?.['fr-FR']?.externalImageUrl"
          class="table-image"
          :src="selectedRecipe[0]?.media?.['fr-FR']?.externalImageUrl || ''"
          @error="$event.target.src = defaultImage"
        />
        <img alt="" v-else :src="defaultImage" />
      </div>
      <div class="recipe-isin">
        <span class="isin-text">{{
          selectedRecipe[0].isin ? selectedRecipe[0].isin : ""
        }}</span>
      </div>
      <div class="recipe-name">
        <span class="name-text">
          {{ selectedRecipe[0]?.title?.["fr-FR"] ?? "" }}
        </span>
      </div>
      <div class="recipe-time">
        {{
          selectedRecipe[0]?.time?.total
            ? parseDurationString(selectedRecipe[0].time.total)
            : ""
        }}
      </div>
      <div class="recipe-ingredients">
        {{ selectedRecipe[0]?.ingredients?.["fr-FR"]?.length ?? "" }}
        ingredients
      </div>
    </div>
    <div
      v-if="selectedCategoryData?.length && contentList[1]?.isChecked"
      class="recipe-section"
    >
      <div class="recipe-image">
        <img
          alt="recipe"
          :src="selectedCategoryData?.[0]?.data?.['fr-FR']?.image"
          @error="$event.target.src = defaultImage"
        />
      </div>
      <div class="recipe-isin">
        <span class="isin-text">{{ selectedCategoryData[0]?.isin }}</span>
      </div>
      <div class="recipe-name">
        <span class="name-text">{{
          selectedCategoryData?.[0]?.data?.["fr-FR"]?.name
        }}</span>
      </div>
      <div class="recipe-time"></div>
      <div class="recipe-ingredients">
        {{ selectedCategoryData[0]?.totalRecipes }} Recipes
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  containerClass: {
    type: String,
    required: true,
  },
  contentList: {
    type: Array,
    required: true,
  },
  selectedArticle: {
    type: Array,
    default: () => [],
  },
  selectRecipe: {
    type: Function,
    required: true,
  },
  selectedRecipe: {
    type: Array,
    default: () => [],
  },
  selectedCategoryData: {
    type: Array,
    default: () => [],
  },
  deleteArticlePopUp: {
    type: Function,
    required: true,
  },
  replaceArticle: {
    type: Function,
    required: true,
  },
  addCategoryButton: {
    type: Function,
    required: true,
  },
  showPreview: {
    type: Boolean,
    default: true,
  },
  isPreviewEnabled: {
    type: Boolean,
    default: false,
  },
  description: {
    type: String,
    default: "",
  },
  ctaInput: {
    type: String,
    default: "",
  },
  isRecipesButtonVisible: {
    type: Boolean,
    default: true,
  },
  isArticlesButtonVisible: {
    type: Boolean,
    default: true,
  },
  selectArticle: {
    type: Function,
    required: true,
  },
  defaultImage: {
    type: String,
    required: true
  },
  updateShowAsterisk: {
    type: Number,
    required: true,
  }
});

const selectedRecipeImage = computed(() => {
  const media = props.selectedRecipe?.[0]?.media?.["fr-FR"];
  return media?.image && !media?.externalImageUrl ? media.image : props.defaultImage;
});

const decreaseHeight = computed(() => {
  return (
    (props.selectedArticle.length && props.contentList[0].isChecked) ||
    (props.selectedRecipe.length && props.contentList[1].isChecked) ||
    (props.selectedCategoryData.length && props.contentList[1].isChecked)
  );
});

const { parseDurationString } = useTimeUtils();
const localDescription = ref(props.description);
const localctaInput = ref(props.ctaInput);
const emit = defineEmits([
  "update:description",
  "update:ctaInput",
  "deleteRecipe",
  "deleteCategory",
  "update:isPreviewEnabled",
]);

const getTime = (jsonTimestamp) => {
  const timestamp = jsonTimestamp * 1000;
  const date = new Date(timestamp);
  const options = { month: "short", day: "numeric", year: "numeric" };
  return date.toLocaleDateString("en-US", options);
};

const togglePreview = () => {
  emit("update:isPreviewEnabled", !props.isPreviewEnabled);
};

const onDescriptionInput = (event) => {
  emit("update:description", event.target.value);
};

const onCTAInput = (event) => {
  emit("update:ctaInput", event.target.value);
};

const deleteRecipe = () => {
  emit("deleteRecipe");
};

const deleteCategory = () => {
  emit("deleteCategory");
};
</script>
