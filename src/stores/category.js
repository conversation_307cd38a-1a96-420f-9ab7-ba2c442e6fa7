import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { useSimpleCustomFetch } from '../composables/useCustomFetch.js';

export const useCategoryStore = defineStore('category', () => {
  // State
  const categoryList = ref([]);
  const categoryData = ref(null);
  const promotedRecipes = ref([]);
  const recipeForCategories = ref([]);
  const isLoading = ref(false);
  const operationStatus = ref('');
  const categorySlug = ref('');
  const categoryAssociations = ref([]);
  const masterListData = ref([]);

  // Pagination
  const pagination = ref({
    from: 0,
    size: 15,
    total: 0,
  });

  // Computed
  const getCategoryList = computed(() => categoryList.value);
  const getCategoryData = computed(() => categoryData.value);
  const getPromotedRecipes = computed(() => promotedRecipes.value);
  const getRecipeForCategories = computed(() => recipeForCategories.value);
  const getIsLoading = computed(() => isLoading.value);
  const getPagination = computed(() => pagination.value);
  const getOperationStatus = computed(() => operationStatus.value);

  // Actions
  const getCategoryListAsync = async (params = {}, { store, $axios } = {}) => {
    if (!store || !$axios) {
      console.error('[IQ][useCategoryStore] Missing required dependencies for getCategoryListAsync');
      return;
    }

    try {
      isLoading.value = true;

      const endpoint = store.getters['config/getClientEndpoint']('flite', 'getCategoryMasterData');
      const baseURL = store.getters['config/getClientConfig']('flite').host;

      const response = await $axios.get(endpoint, {
        baseURL,
        params,
      });

      if (response?.data?.results) {
        categoryList.value = response.data.results;
        pagination.value = {
          ...pagination.value,
          total: response.data.total || 0
        };
      }
    } catch (error) {
      console.error('[IQ][useCategoryStore] Error fetching category list:', error);
    } finally {
      isLoading.value = false;
    }
  };

  const getCategoryDataAsync = async (isin, lang, { store, $axios } = {}) => {
    if (!store || !$axios) {
      console.error('[IQ][useCategoryStore] Missing required dependencies for getCategoryDataAsync');
      return;
    }

    try {
      isLoading.value = true;

      let endpoint = store.getters['config/getClientEndpoint']('flite', 'getCategoryData');
      const baseURL = store.getters['config/getClientConfig']('flite').host;
      const params = { lang, includeAlerts: true };
      endpoint = `${endpoint}/${isin}`;

      const response = await $axios.get(endpoint, {
        baseURL,
        params,
      });

      if (response?.data) {
        categoryData.value = response.data;
      }
    } catch (error) {
      console.error('[IQ][useCategoryStore] Error fetching category data:', error);
    } finally {
      isLoading.value = false;
    }
  };

  const getRecipeForCategoriesAsync = async (params) => {
    try {
      const response = await useSimpleCustomFetch('', { params }, 'icl', 'getRecipeList');

      if (response) {
        recipeForCategories.value = response;
      }
    } catch (error) {
      console.error('[IQ][useCategoryStore] Error fetching recipes for categories:', error);
    }
  };

  const postCategoryAsync = async (payload, lang) => {
    try {
      const params = {
        country: lang.split('-')[1],
        lang: lang.split('-')[0]
      };
      const response = await useSimpleCustomFetch('', {
        method: 'POST',
        body: payload,
        params
      }, 'icl', 'saveTag');

      return response;
    } catch (error) {
      console.error('[IQ][useCategoryStore] Error creating category:', error);
      throw error;
    }
  };

  const patchCategoryAsync = async (payload, isin) => {
    try {
      const response = await useSimpleCustomFetch('', {
        method: 'PATCH',
        body: payload
      }, 'icl', 'saveTag', isin);

      return response;
    } catch (error) {
      console.error('[IQ][useCategoryStore] Error updating category:', error);
      throw error;
    }
  };

  const checkSlugAsync = async (slug, lang) => {
    try {
      const params = { slug, lang };
      const response = await useSimpleCustomFetch('', { params }, 'flite', 'getCategorySlug');

      categorySlug.value = response;
      return response;
    } catch (error) {
      console.error('[IQ][useCategoryStore] Error checking slug:', error);
      throw error;
    }
  };

  const postCategoryRecipeAsync = async (payload) => {
    try {
      const response = await useSimpleCustomFetch('', {
        method: 'POST',
        body: payload
      }, 'flite', 'postAssociateOperation');

      return response;
    } catch (error) {
      console.error('[IQ][useCategoryStore] Error in postCategoryRecipeAsync:', error);
      throw error;
    }
  };

  const getOperationStatusAsync = async (operationId) => {
    try {
      const response = await useSimpleCustomFetch('', {}, 'icl', 'getOperationStatus', operationId, '{opId}');

      operationStatus.value = response;
      return response;
    } catch (error) {
      console.error('[IQ][useCategoryStore] Error getting operation status:', error);
      throw error;
    }
  };

  const saveRecipeCampaignDataAsync = async (payload, lang) => {
    try {
      const params = { lang };
      const response = await useSimpleCustomFetch('', {
        method: 'POST',
        body: payload,
        params
      }, 'flite', 'postCategoryCampaign');

      return response;
    } catch (error) {
      console.error('[IQ][useCategoryStore] Error saving recipe campaign data:', error);
      throw error;
    }
  };

  const getPromotedRecipesForCategoriesAsync = async (isin, lang) => {
    if (!isin || !lang) {
      console.error('[IQ][useCategoryStore] Missing required parameters for getPromotedRecipesForCategoriesAsync');
      return;
    }

    try {
      const params = { lang, includePromotedRecipeDetails: true };
      const response = await useSimpleCustomFetch('', {
        params
      }, 'flite', 'getCategoryCampaign', isin, '{isin}');

      if (response) {
        promotedRecipes.value = response;
      }
      return response;
    } catch (error) {
      console.error('[IQ][useCategoryStore] Error fetching promoted recipes for categories:', error);
      throw error;
    }
  };

  const getCategoryAssociationsAsync = async (isin, lang, from, size) => {
    if (!isin || !lang) {
      console.error('[IQ][useCategoryStore] Missing required parameters for getCategoryAssociationsAsync');
      return;
    }

    try {
      const params = { lang, from, size };
      const response = await useSimpleCustomFetch('', {
        params
      }, 'flite', 'getCategoryAssociations', isin, '{isin}');

      if (response) {
        categoryAssociations.value = response;
      }
      return response;
    } catch (error) {
      console.error('[IQ][useCategoryStore] Error fetching category associations:', error);
      throw error;
    }
  };

  const getEditCategoryGroupListAsync = async (isin, lang, sectionType) => {
    if (!isin || !lang) {
      console.error('[IQ][useCategoryStore] Missing required parameters for getEditCategoryGroupListAsync');
      return;
    }

    try {
      const params = { type: sectionType };
      const response = await useSimpleCustomFetch('', {
        params
      }, 'icl', 'saveTag', isin);

      return response;
    } catch (error) {
      console.error('[IQ][useCategoryStore] Error fetching edit category group list:', error);
      throw error;
    }
  };

  const deleteLanguageVariantAsync = async (isin, langArray) => {
    if (!isin || !langArray) {
      console.error('[IQ][useCategoryStore] Missing required parameters for deleteLanguageVariantAsync');
      return;
    }

    try {
      const response = await useSimpleCustomFetch('', {
        method: 'DELETE',
        body: { lang: langArray }
      }, 'icl', 'saveTag', isin);

      return response;
    } catch (error) {
      console.error('[IQ][useCategoryStore] Error deleting language variant:', error);
      throw error;
    }
  };

  const deleteCategoryList = async (isin) => {
    if (!isin) {
      console.error('[IQ][useCategoryStore] Missing required parameters for deleteCategoryList');
      return;
    }

    try {
      const response = await useSimpleCustomFetch('', {
        method: 'DELETE'
      }, 'icl', 'saveTag', isin);

      return response;
    } catch (error) {
      console.error('[IQ][useCategoryStore] Error deleting category:', error);
      throw error;
    }
  };

  const resetStore = () => {
    categoryList.value = [];
    categoryData.value = null;
    promotedRecipes.value = [];
    recipeForCategories.value = [];
    operationStatus.value = '';
    categorySlug.value = '';
    categoryAssociations.value = [];
    masterListData.value = [];
    pagination.value = {
      from: 0,
      size: 15,
      total: 0,
    };
  };

  return {
    // State
    categoryList,
    categoryData,
    promotedRecipes,
    recipeForCategories,
    isLoading,
    operationStatus,
    categorySlug,
    categoryAssociations,
    masterListData,
    pagination,

    // Computed
    getCategoryList,
    getCategoryData,
    getPromotedRecipes,
    getRecipeForCategories,
    getIsLoading,
    getPagination,
    getOperationStatus,

    // Actions
    getCategoryListAsync,
    getCategoryDataAsync,
    getRecipeForCategoriesAsync,
    postCategoryAsync,
    patchCategoryAsync,
    checkSlugAsync,
    postCategoryRecipeAsync,
    getOperationStatusAsync,
    saveRecipeCampaignDataAsync,
    getPromotedRecipesForCategoriesAsync,
    getCategoryAssociationsAsync,
    getEditCategoryGroupListAsync,
    deleteLanguageVariantAsync,
    deleteCategoryList,
    resetStore,
  };
});
